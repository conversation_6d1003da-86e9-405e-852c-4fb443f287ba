#include "tlc5615.h"


//****************TLC5615数据发送函数
//da:要发送的DA值(0-1023)
void write_5615(uint16_t da)
{
   uint16_t i;

   // 限制输入范围到10位
   da = da & 0x03FF;  // 确保只有10位有效

   // 将10位数据左对齐到12位(高10位为数据，低2位为0)
   da = da << 2;

   // 开始SPI时序
   CS_1();           // CS先拉高
   SCK_0();          // 时钟先拉低
   Delay_us(1);      // 短暂延时确保信号稳定
   CS_0();           // 片选拉低，开始传输

   // 发送12位数据，MSB先发送
   for(i = 0; i < 12; i++)
   {
     // 检查最高位
     if((da & 0x0800) == 0x0800)  // 检查第11位(最高位)
       DI_1();
     else
       DI_0();

     Delay_us(1);     // 数据建立时间
     SCK_1();         // 时钟上升沿，TLC5615在此时采样数据
     Delay_us(1);     // 时钟高电平保持时间
     da <<= 1;        // 左移准备下一位
     SCK_0();         // 时钟下降沿
   }

   // 传输完成，释放片选
   Delay_us(1);
   CS_1();
   Delay_us(2);       // 确保DAC有足够时间更新输出
}

//****************TLC5615测试函数
//测试不同数值的输出电压
void test_5615_voltage(void)
{
    // 测试关键数值点
    write_5615(0);      // 应输出0V
    delay_ms(1000);

    write_5615(256);    // 应输出VREF/4 (约1V)
    delay_ms(1000);

    write_5615(512);    // 应输出VREF/2 (约2V)
    delay_ms(1000);

    write_5615(768);    // 应输出3*VREF/4 (约3V)
    delay_ms(1000);

    write_5615(1023);   // 应输出VREF (约4V)
    delay_ms(1000);
}











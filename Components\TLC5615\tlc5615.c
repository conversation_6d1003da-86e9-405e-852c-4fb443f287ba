#include "tlc5615.h"


//****************tlc5615数据发送函数
//da:要发送的DA值
void write_5615(uint16_t da)
{
   uint16_t i;
   CS_1();
   SCK_0();
   CS_0();
   //da<<=6;//有效位位10位去掉没用的6位
   da=da&0x03ff;
   for(i=0;i<12;i++)
   {
     if((da&0x0200)==0x0200)//此时是10位数据0是后面添加的在此处理
      DI_1();
     else
      DI_0();
     SCK_1();
     da<<=1;
     SCK_0();

    }
    SCK_0();
    CS_1();
    Delay_us(2);
}











#include "tlc5615.h"


//****************tlc5615数据发送函数
//da:要发送的DA值(0-1023)
void write_5615(uint16_t da)
{
   uint16_t i;

   CS_1();
   SCK_0();
   CS_0();

   // 限制到10位有效数据
   da = da & 0x03ff;

   // 将10位数据左移2位，对齐到12位格式(高10位数据，低2位为0)
   da = da << 2;

   // 发送12位数据，MSB先发
   for(i = 0; i < 12; i++)
   {
     if((da & 0x0800) == 0x0800)  // 检查最高位(第11位)
      DI_1();
     else
      DI_0();
     SCK_1();
     da <<= 1;  // 左移准备下一位
     SCK_0();
   }

   SCK_0();
   CS_1();
   Delay_us(2);
}

//****************TLC5615电压测试函数
//测试关键数值点的电压输出
void test_tlc5615_voltage(void)
{
    // 测试0V
    write_5615(0);
    delay_ms(2000);  // 2秒延时便于测量

    // 测试1/4满量程 (约VREF/4)
    write_5615(256);
    delay_ms(2000);

    // 测试问题数值300 (应该约VREF*300/1023)
    write_5615(300);
    delay_ms(2000);

    // 测试1/2满量程 (约VREF/2)
    write_5615(512);
    delay_ms(2000);

    // 测试3/4满量程 (约3*VREF/4)
    write_5615(768);
    delay_ms(2000);

    // 测试满量程 (VREF)
    write_5615(1023);
    delay_ms(2000);
}











# TLC5615 DAC 波形生成器

## 问题分析与解决方案

### 原始问题
您的TLC5615代码输出3V左右的直流电压而不是预期的方波三角波，主要原因如下：

1. **三角波逻辑错误**：变量`j`在第一次循环后保持在500附近，无法重置
2. **数值范围问题**：使用500作为最大值，对应约2.44V，平均值约1.22V
3. **延时过长**：10ms延时导致频率过低，看起来像直流
4. **数据范围错误**：注释说范围0~1024，实际10位DAC范围是0~1023

### 修复内容

#### 1. 修正了TLC5615函数注释
- 更新了`tlc5615_Send12Bit`函数注释，明确数据范围为0~1023

#### 2. 重写了波形生成函数
```c
void generate_Wave(void)
{
    uint16_t wave_max = 1023;  // TLC5615最大值(10位DAC)
    uint16_t wave_min = 0;     // 最小值
    
    while(1)
    {
        // 生成方波 (0V到5V)
        for(n=0; n<50; n++)
        {
           tlc5615_Send12Bit(wave_min);    // 0V
           delay_ms(1);                    // 1ms延时
           tlc5615_Send12Bit(wave_max);    // 5V
           delay_ms(1);                    // 1ms延时
        }

        // 生成三角波
        for(n=0; n<10; n++)
        {
            // 上升沿：从0到1023
            for(j=wave_min; j<=wave_max; j+=8)
            {
               tlc5615_Send12Bit(j);
               Delay_us(50);  // 50us延时
            }
            // 下降沿：从1023到0
            for(j=wave_max; j>wave_min; j-=8)
            {
                tlc5615_Send12Bit(j);
                Delay_us(50);  // 50us延时
            } 
        }
    }
}
```

#### 3. 新增独立波形生成函数
- `generate_SquareWave(uint16_t cycles, uint16_t delay_ms)` - 生成方波
- `generate_TriangleWave(uint16_t cycles, uint16_t delay_us)` - 生成三角波

#### 4. 更新主程序调用方式
```c
while (1)
{
    // 测试方波：50个周期，每个电平持续2ms
    generate_SquareWave(50, 2);
    
    // 测试三角波：10个周期，每步延时50us
    generate_TriangleWave(10, 50);
}
```

## 硬件连接
- SCK: PD3
- DIN: PD2  
- CS:  PD4
- DOUT: PD5 (输入)

## 输出电压计算
- 输出电压 = (数字值 / 1023) × Vref
- 如果Vref = 5V：
  - 数字值0 → 0V
  - 数字值1023 → 5V
  - 数字值512 → 约2.5V

## 使用说明

### 方波生成
```c
generate_SquareWave(50, 2);  // 50个周期，每个电平2ms
```

### 三角波生成
```c
generate_TriangleWave(10, 50);  // 10个周期，每步50us
```

### 自定义波形
可以直接调用`tlc5615_Send12Bit(value)`发送0-1023范围内的任意值。

## 注意事项
1. TLC5615是10位DAC，有效数据范围0-1023
2. 确保参考电压Vref连接正确
3. 调整延时参数可以改变波形频率
4. 步进值影响波形的平滑度（当前设置为8）

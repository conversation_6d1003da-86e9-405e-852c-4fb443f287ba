Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.Delay_us) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.delay_ms) refers to stm32f4xx_hal.o(.data) for uwTick
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to waveform_analyzer_app.o(i.My_FFT_Init) for My_FFT_Init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to adc_app.o(i.adc_tim_dma_init) for adc_tim_dma_init
    main.o(i.main) refers to tlc5615.o(i.test_tlc5615_voltage) for test_tlc5615_voltage
    main.o(i.main) refers to main.o(i.delay_ms) for delay_ms
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to uart_app.o(.bss) for uart_rx_dma_buffer
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_hal_msp.o(i.HAL_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit) for FSMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to lcd.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init) for FSMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init) for FSMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) for SRAM_DMACpltProt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable) for FSMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable) for FSMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    lcd.o(i.HAL_SRAM_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.lcd_clear) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_clear) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_clear) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_color_fill) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_color_fill) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_display_dir) refers to lcd.o(i.lcd_scan_dir) for lcd_scan_dir
    lcd.o(i.lcd_display_dir) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_display_off) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_display_off) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_display_on) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_display_on) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_draw_circle) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_draw_hline) refers to lcd.o(i.lcd_fill) for lcd_fill
    lcd.o(i.lcd_draw_hline) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_draw_line) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_draw_point) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_draw_point) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_draw_rectangle) refers to lcd.o(i.lcd_draw_line) for lcd_draw_line
    lcd.o(i.lcd_ex_ili9341_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ili9341_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ili9341_reginit) refers to main.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_ili9806_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ili9806_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ili9806_reginit) refers to main.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_nt35310_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_nt35310_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_nt35310_reginit) refers to main.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_nt35510_reginit) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_ex_nt35510_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_nt35510_reginit) refers to main.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to main.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_st7789_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_st7789_reginit) refers to main.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_st7789_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_st7796_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_st7796_reginit) refers to main.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_st7796_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_fill) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_fill) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_fill_circle) refers to lcd.o(i.lcd_draw_hline) for lcd_draw_hline
    lcd.o(i.lcd_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.lcd_init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    lcd.o(i.lcd_init) refers to main.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_rd_data) for lcd_rd_data
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_st7789_reginit) for lcd_ex_st7789_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ili9341_reginit) for lcd_ex_ili9341_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_nt35310_reginit) for lcd_ex_nt35310_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_st7796_reginit) for lcd_ex_st7796_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_nt35510_reginit) for lcd_ex_nt35510_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ili9806_reginit) for lcd_ex_ili9806_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ssd1963_reginit) for lcd_ex_ssd1963_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ssd_backlight_set) for lcd_ssd_backlight_set
    lcd.o(i.lcd_init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_display_dir) for lcd_display_dir
    lcd.o(i.lcd_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_clear) for lcd_clear
    lcd.o(i.lcd_init) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_rd_data) for lcd_rd_data
    lcd.o(i.lcd_read_point) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_scan_dir) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_set_cursor) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_set_cursor) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_set_cursor) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_set_window) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_set_window) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_set_window) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_show_char) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_show_char) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.lcd_show_char) refers to lcd.o(.data) for .data
    lcd.o(i.lcd_show_char) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_show_float) refers to lcd.o(i.lcd_show_num) for lcd_show_num
    lcd.o(i.lcd_show_float) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_show_float) refers to lcd.o(i.lcd_show_xnum) for lcd_show_xnum
    lcd.o(i.lcd_show_num) refers to lcd.o(i.lcd_pow) for lcd_pow
    lcd.o(i.lcd_show_num) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_show_string) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_show_xnum) refers to lcd.o(i.lcd_pow) for lcd_pow
    lcd.o(i.lcd_show_xnum) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_ssd_backlight_set) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ssd_backlight_set) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ssd_backlight_set) refers to dfltui.o(.text) for __aeabi_ui2d
    lcd.o(i.lcd_ssd_backlight_set) refers to dmul.o(.text) for __aeabi_dmul
    lcd.o(i.lcd_ssd_backlight_set) refers to dfixui.o(.text) for __aeabi_d2uiz
    lcd.o(i.lcd_write_ram_prepare) refers to lcd.o(.bss) for .bss
    tlc5615.o(i.test_tlc5615_voltage) refers to tlc5615.o(i.write_5615) for write_5615
    tlc5615.o(i.test_tlc5615_voltage) refers to main.o(i.delay_ms) for delay_ms
    tlc5615.o(i.write_5615) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tlc5615.o(i.write_5615) refers to main.o(i.Delay_us) for Delay_us
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(.data) refers to adc_app.o(i.adc_task) for adc_task
    led_app.o(i.led_disp) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_app.o(i.led_disp) refers to led_app.o(.data) for .data
    led_app.o(i.led_task) refers to led_app.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for .data
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memcpya.o(.text) for __aeabi_memcpy
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.data) for .data
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal.o(.data) for uwTick
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to uart_app.o(.data) for .data
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    uart_app.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_app.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_app.o(i.uart_task) refers to uart_app.o(i.my_printf) for my_printf
    uart_app.o(i.uart_task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.uart_task) refers to uart_app.o(.data) for .data
    uart_app.o(i.uart_task) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.uart_task) refers to usart.o(.bss) for huart1
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss) for hadc1
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to waveform_analyzer_app.o(i.Get_Waveform_Info) for Get_Waveform_Info
    adc_app.o(i.adc_task) refers to adc_app.o(i.print_harmonic_data) for print_harmonic_data
    adc_app.o(i.adc_task) refers to adc_app.o(i.display_harmonic_results) for display_harmonic_results
    adc_app.o(i.adc_task) refers to memseta.o(.text) for __aeabi_memclr4
    adc_app.o(i.adc_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_task) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_task) refers to adc.o(.bss) for hadc1
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_tim_dma_init) refers to tim.o(.bss) for htim3
    adc_app.o(i.adc_tim_dma_init) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_tim_dma_init) refers to adc.o(.bss) for hadc1
    adc_app.o(i.display_harmonic_results) refers to lcd.o(i.lcd_fill) for lcd_fill
    adc_app.o(i.display_harmonic_results) refers to lcd.o(i.lcd_show_string) for lcd_show_string
    adc_app.o(i.display_harmonic_results) refers to lcd.o(i.lcd_show_float) for lcd_show_float
    adc_app.o(i.print_harmonic_data) refers to f2d.o(.text) for __aeabi_f2d
    adc_app.o(i.print_harmonic_data) refers to uart_app.o(i.my_printf) for my_printf
    adc_app.o(i.print_harmonic_data) refers to usart.o(.bss) for huart1
    waveform_analyzer_app.o(i.Analyze_Frequency_And_Type) refers to transformfunctions.o(i.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    waveform_analyzer_app.o(i.Analyze_Frequency_And_Type) refers to complexmathfunctions.o(i.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    waveform_analyzer_app.o(i.Analyze_Frequency_And_Type) refers to waveform_analyzer_app.o(.bss) for .bss
    waveform_analyzer_app.o(i.Analyze_Harmonics) refers to transformfunctions.o(i.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    waveform_analyzer_app.o(i.Analyze_Harmonics) refers to complexmathfunctions.o(i.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    waveform_analyzer_app.o(i.Analyze_Harmonics) refers to waveform_analyzer_app.o(i.Get_Component_Phase) for Get_Component_Phase
    waveform_analyzer_app.o(i.Analyze_Harmonics) refers to waveform_analyzer_app.o(i.Calculate_Phase_Difference) for Calculate_Phase_Difference
    waveform_analyzer_app.o(i.Analyze_Harmonics) refers to waveform_analyzer_app.o(.bss) for .bss
    waveform_analyzer_app.o(i.Get_Component_Phase) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    waveform_analyzer_app.o(i.Get_Phase_Difference) refers to waveform_analyzer_app.o(i.Get_Waveform_Phase) for Get_Waveform_Phase
    waveform_analyzer_app.o(i.Get_Phase_Difference) refers to waveform_analyzer_app.o(i.Calculate_Phase_Difference) for Calculate_Phase_Difference
    waveform_analyzer_app.o(i.Get_Waveform_Frequency) refers to waveform_analyzer_app.o(i.Perform_FFT) for Perform_FFT
    waveform_analyzer_app.o(i.Get_Waveform_Frequency) refers to waveform_analyzer_app.o(.bss) for .bss
    waveform_analyzer_app.o(i.Get_Waveform_Info) refers to memcpya.o(.text) for __aeabi_memcpy4
    waveform_analyzer_app.o(i.Get_Waveform_Info) refers to waveform_analyzer_app.o(i.Get_Waveform_Vpp) for Get_Waveform_Vpp
    waveform_analyzer_app.o(i.Get_Waveform_Info) refers to waveform_analyzer_app.o(i.Analyze_Frequency_And_Type) for Analyze_Frequency_And_Type
    waveform_analyzer_app.o(i.Get_Waveform_Info) refers to waveform_analyzer_app.o(i.Get_Waveform_Phase) for Get_Waveform_Phase
    waveform_analyzer_app.o(i.Get_Waveform_Info) refers to waveform_analyzer_app.o(i.Analyze_Harmonics) for Analyze_Harmonics
    waveform_analyzer_app.o(i.Get_Waveform_Info) refers to waveform_analyzer_app.o(.constdata) for .constdata
    waveform_analyzer_app.o(i.Get_Waveform_Phase) refers to transformfunctions.o(i.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    waveform_analyzer_app.o(i.Get_Waveform_Phase) refers to waveform_analyzer_app.o(i.Map_Input_To_FFT_Frequency) for Map_Input_To_FFT_Frequency
    waveform_analyzer_app.o(i.Get_Waveform_Phase) refers to waveform_analyzer_app.o(i.Get_Component_Phase) for Get_Component_Phase
    waveform_analyzer_app.o(i.Get_Waveform_Phase) refers to waveform_analyzer_app.o(.bss) for .bss
    waveform_analyzer_app.o(i.Get_Waveform_Phase_ZeroCrossing) refers to waveform_analyzer_app.o(i.Map_Input_To_FFT_Frequency) for Map_Input_To_FFT_Frequency
    waveform_analyzer_app.o(i.Get_Waveform_Type) refers to waveform_analyzer_app.o(i.Analyze_Frequency_And_Type) for Analyze_Frequency_And_Type
    waveform_analyzer_app.o(i.Get_Waveform_Vpp) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    waveform_analyzer_app.o(i.Map_FFT_To_Input_Frequency) refers to memcpya.o(.text) for __aeabi_memcpy4
    waveform_analyzer_app.o(i.Map_FFT_To_Input_Frequency) refers to waveform_analyzer_app.o(.constdata) for .constdata
    waveform_analyzer_app.o(i.My_FFT_Init) refers to transformfunctions.o(i.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    waveform_analyzer_app.o(i.My_FFT_Init) refers to waveform_analyzer_app.o(.bss) for .bss
    waveform_analyzer_app.o(i.Perform_FFT) refers to transformfunctions.o(i.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    waveform_analyzer_app.o(i.Perform_FFT) refers to complexmathfunctions.o(i.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    waveform_analyzer_app.o(i.Perform_FFT) refers to waveform_analyzer_app.o(.bss) for .bss
    basicmathfunctions.o(i.arm_abs_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    basicmathfunctions.o(i.arm_add_f64) refers to dadd.o(.text) for __aeabi_dadd
    basicmathfunctions.o(i.arm_dot_prod_f64) refers to dmul.o(.text) for __aeabi_dmul
    basicmathfunctions.o(i.arm_dot_prod_f64) refers to dadd.o(.text) for __aeabi_dadd
    basicmathfunctions.o(i.arm_mult_f64) refers to dmul.o(.text) for __aeabi_dmul
    basicmathfunctions.o(i.arm_offset_f64) refers to dadd.o(.text) for __aeabi_dadd
    basicmathfunctions.o(i.arm_scale_f64) refers to dmul.o(.text) for __aeabi_dmul
    basicmathfunctions.o(i.arm_shift_q31) refers to llshl.o(.text) for __aeabi_llsl
    basicmathfunctions.o(i.arm_sub_f64) refers to dadd.o(.text) for __aeabi_dsub
    bayesfunctions.o(i.arm_gaussian_naive_bayes_predict_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    bayesfunctions.o(i.arm_gaussian_naive_bayes_predict_f32) refers to statisticsfunctions.o(i.arm_max_f32) for arm_max_f32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_16_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_32_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_64_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_128_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_256_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_512_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_1024_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_2048_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_4096_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_16_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_32_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_64_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_128_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_256_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_512_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_1024_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_2048_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_4096_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len16
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len32
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len64
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len128
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len256
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len512
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len16
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len32
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len64
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len128
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len256
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len512
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len4096
    complexmathfunctions.o(i.arm_cmplx_mag_f64) refers to dmul.o(.text) for __aeabi_dmul
    complexmathfunctions.o(i.arm_cmplx_mag_f64) refers to dadd.o(.text) for __aeabi_dadd
    complexmathfunctions.o(i.arm_cmplx_mag_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    complexmathfunctions.o(i.arm_cmplx_mag_fast_q15) refers to fastmathfunctions.o(i.arm_sqrt_q15) for arm_sqrt_q15
    complexmathfunctions.o(i.arm_cmplx_mag_q15) refers to fastmathfunctions.o(i.arm_sqrt_q31) for arm_sqrt_q31
    complexmathfunctions.o(i.arm_cmplx_mag_q31) refers to fastmathfunctions.o(i.arm_sqrt_q31) for arm_sqrt_q31
    complexmathfunctions.o(i.arm_cmplx_mag_squared_f64) refers to dmul.o(.text) for __aeabi_dmul
    complexmathfunctions.o(i.arm_cmplx_mag_squared_f64) refers to dadd.o(.text) for __aeabi_dadd
    complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f64) refers to dmul.o(.text) for __aeabi_dmul
    complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f64) refers to dadd.o(.text) for __aeabi_dsub
    controllerfunctions.o(i.arm_sin_cos_f32) refers to commontables.o(.constdata) for sinTable_f32
    controllerfunctions.o(i.arm_sin_cos_q31) refers to commontables.o(.constdata) for sinTable_q31
    distancefunctions.o(i.arm_chebyshev_distance_f64) refers to dadd.o(.text) for __aeabi_dsub
    distancefunctions.o(i.arm_chebyshev_distance_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    distancefunctions.o(i.arm_chebyshev_distance_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    distancefunctions.o(i.arm_cityblock_distance_f64) refers to dadd.o(.text) for __aeabi_dsub
    distancefunctions.o(i.arm_cityblock_distance_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    distancefunctions.o(i.arm_correlation_distance_f32) refers to statisticsfunctions.o(i.arm_mean_f32) for arm_mean_f32
    distancefunctions.o(i.arm_correlation_distance_f32) refers to basicmathfunctions.o(i.arm_offset_f32) for arm_offset_f32
    distancefunctions.o(i.arm_correlation_distance_f32) refers to statisticsfunctions.o(i.arm_power_f32) for arm_power_f32
    distancefunctions.o(i.arm_correlation_distance_f32) refers to basicmathfunctions.o(i.arm_dot_prod_f32) for arm_dot_prod_f32
    distancefunctions.o(i.arm_cosine_distance_f32) refers to statisticsfunctions.o(i.arm_power_f32) for arm_power_f32
    distancefunctions.o(i.arm_cosine_distance_f32) refers to basicmathfunctions.o(i.arm_dot_prod_f32) for arm_dot_prod_f32
    distancefunctions.o(i.arm_cosine_distance_f64) refers to statisticsfunctions.o(i.arm_power_f64) for arm_power_f64
    distancefunctions.o(i.arm_cosine_distance_f64) refers to basicmathfunctions.o(i.arm_dot_prod_f64) for arm_dot_prod_f64
    distancefunctions.o(i.arm_cosine_distance_f64) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_cosine_distance_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    distancefunctions.o(i.arm_cosine_distance_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_cosine_distance_f64) refers to dadd.o(.text) for __aeabi_drsub
    distancefunctions.o(i.arm_dice_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_TF_FT) for arm_boolean_distance_TT_TF_FT
    distancefunctions.o(i.arm_dice_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_dice_distance) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_dice_distance) refers to dadd.o(.text) for __aeabi_dadd
    distancefunctions.o(i.arm_dice_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_dice_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_euclidean_distance_f64) refers to dadd.o(.text) for __aeabi_dsub
    distancefunctions.o(i.arm_euclidean_distance_f64) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_euclidean_distance_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    distancefunctions.o(i.arm_hamming_distance) refers to distancefunctions.o(i.arm_boolean_distance_TF_FT) for arm_boolean_distance_TF_FT
    distancefunctions.o(i.arm_hamming_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_hamming_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_hamming_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_jaccard_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_TF_FT) for arm_boolean_distance_TT_TF_FT
    distancefunctions.o(i.arm_jaccard_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_jaccard_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_jaccard_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_jensenshannon_distance_f32) refers to distancefunctions.o(i.rel_entr) for rel_entr
    distancefunctions.o(i.arm_kulsinski_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_TF_FT) for arm_boolean_distance_TT_TF_FT
    distancefunctions.o(i.arm_kulsinski_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_kulsinski_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_kulsinski_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_minkowski_distance_f32) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    distancefunctions.o(i.arm_rogerstanimoto_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_FF_TF_FT) for arm_boolean_distance_TT_FF_TF_FT
    distancefunctions.o(i.arm_rogerstanimoto_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_rogerstanimoto_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_rogerstanimoto_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_russellrao_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT) for arm_boolean_distance_TT
    distancefunctions.o(i.arm_sokalmichener_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_FF_TF_FT) for arm_boolean_distance_TT_FF_TF_FT
    distancefunctions.o(i.arm_sokalmichener_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_sokalmichener_distance) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_sokalmichener_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_sokalsneath_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_TF_FT) for arm_boolean_distance_TT_TF_FT
    distancefunctions.o(i.arm_sokalsneath_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_sokalsneath_distance) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_sokalsneath_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_yule_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_FF_TF_FT) for arm_boolean_distance_TT_FF_TF_FT
    distancefunctions.o(i.arm_yule_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_yule_distance) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_yule_distance) refers to dadd.o(.text) for __aeabi_dadd
    distancefunctions.o(i.arm_yule_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_yule_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.rel_entr) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    fastmathfunctions.o(i.arm_atan2_f32) refers to fastmathfunctions.o(.constdata) for .constdata
    fastmathfunctions.o(i.arm_atan2_q15) refers to fastmathfunctions.o(i.arm_divide_q15) for arm_divide_q15
    fastmathfunctions.o(i.arm_atan2_q15) refers to fastmathfunctions.o(.constdata) for .constdata
    fastmathfunctions.o(i.arm_atan2_q31) refers to fastmathfunctions.o(i.arm_divide_q31) for arm_divide_q31
    fastmathfunctions.o(i.arm_atan2_q31) refers to llshl.o(.text) for __aeabi_llsl
    fastmathfunctions.o(i.arm_atan2_q31) refers to fastmathfunctions.o(.constdata) for .constdata
    fastmathfunctions.o(i.arm_cos_f32) refers to commontables.o(.constdata) for sinTable_f32
    fastmathfunctions.o(i.arm_cos_q15) refers to commontables.o(.constdata) for sinTable_q15
    fastmathfunctions.o(i.arm_cos_q31) refers to commontables.o(.constdata) for sinTable_q31
    fastmathfunctions.o(i.arm_divide_q15) refers to basicmathfunctions.o(i.arm_abs_q15) for arm_abs_q15
    fastmathfunctions.o(i.arm_divide_q31) refers to basicmathfunctions.o(i.arm_abs_q31) for arm_abs_q31
    fastmathfunctions.o(i.arm_divide_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    fastmathfunctions.o(i.arm_divide_q31) refers to llsshr.o(.text) for __aeabi_lasr
    fastmathfunctions.o(i.arm_sin_f32) refers to commontables.o(.constdata) for sinTable_f32
    fastmathfunctions.o(i.arm_sin_q15) refers to commontables.o(.constdata) for sinTable_q15
    fastmathfunctions.o(i.arm_sin_q31) refers to commontables.o(.constdata) for sinTable_q31
    fastmathfunctions.o(i.arm_sqrt_q15) refers to commontables.o(.constdata) for sqrt_initial_lut_q15
    fastmathfunctions.o(i.arm_sqrt_q31) refers to commontables.o(.constdata) for sqrt_initial_lut_q31
    fastmathfunctions.o(i.arm_vexp_f32) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    fastmathfunctions.o(i.arm_vexp_f64) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    fastmathfunctions.o(i.arm_vlog_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    fastmathfunctions.o(i.arm_vlog_f64) refers to log.o(i.__hardfp_log) for __hardfp_log
    filteringfunctions.o(i.arm_biquad_cas_df1_32x64_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cas_df1_32x64_q31) refers to llshl.o(.text) for __aeabi_llsl
    filteringfunctions.o(i.arm_biquad_cascade_df1_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cascade_df1_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_biquad_cascade_df1_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cascade_df1_q31) refers to llsshr.o(.text) for __aeabi_lasr
    filteringfunctions.o(i.arm_biquad_cascade_df2T_f64) refers to dmul.o(.text) for __aeabi_dmul
    filteringfunctions.o(i.arm_biquad_cascade_df2T_f64) refers to dadd.o(.text) for __aeabi_dadd
    filteringfunctions.o(i.arm_biquad_cascade_df2T_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cascade_df2T_init_f64) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cascade_stereo_df2T_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_conv_fast_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_fast_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_conv_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_conv_opt_q7) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_partial_fast_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_partial_fast_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_conv_partial_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_partial_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_conv_partial_opt_q7) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_correlate_f64) refers to dmul.o(.text) for __aeabi_dmul
    filteringfunctions.o(i.arm_correlate_f64) refers to dadd.o(.text) for __aeabi_dadd
    filteringfunctions.o(i.arm_correlate_fast_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_correlate_fast_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_correlate_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_correlate_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_correlate_opt_q7) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_fir_decimate_f64) refers to dmul.o(.text) for __aeabi_dmul
    filteringfunctions.o(i.arm_fir_decimate_f64) refers to dadd.o(.text) for __aeabi_dadd
    filteringfunctions.o(i.arm_fir_decimate_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_decimate_init_f64) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_decimate_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_decimate_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_f64) refers to dmul.o(.text) for __aeabi_dmul
    filteringfunctions.o(i.arm_fir_f64) refers to dadd.o(.text) for __aeabi_dadd
    filteringfunctions.o(i.arm_fir_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_init_f64) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_init_q7) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_interpolate_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_interpolate_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_interpolate_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_lattice_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_lattice_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_lattice_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_sparse_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_sparse_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_sparse_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_sparse_init_q7) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_iir_lattice_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_iir_lattice_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_iir_lattice_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_levinson_durbin_q31) refers to fastmathfunctions.o(i.arm_divide_q15) for arm_divide_q15
    filteringfunctions.o(i.arm_lms_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_lms_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_lms_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_lms_norm_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_lms_norm_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_lms_norm_init_q15) refers to commontables.o(.constdata) for armRecipTableQ15
    filteringfunctions.o(i.arm_lms_norm_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_lms_norm_init_q31) refers to commontables.o(.constdata) for armRecipTableQ31
    filteringfunctions.o(i.arm_lms_norm_q31) refers to llsshr.o(.text) for __aeabi_lasr
    matrixfunctions.o(i.arm_householder_f32) refers to basicmathfunctions.o(i.arm_dot_prod_f32) for arm_dot_prod_f32
    matrixfunctions.o(i.arm_householder_f32) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_householder_f32) refers to basicmathfunctions.o(i.arm_scale_f32) for arm_scale_f32
    matrixfunctions.o(i.arm_householder_f64) refers to basicmathfunctions.o(i.arm_dot_prod_f64) for arm_dot_prod_f64
    matrixfunctions.o(i.arm_householder_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matrixfunctions.o(i.arm_householder_f64) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_householder_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_householder_f64) refers to dadd.o(.text) for __aeabi_dadd
    matrixfunctions.o(i.arm_householder_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    matrixfunctions.o(i.arm_householder_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    matrixfunctions.o(i.arm_householder_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_householder_f64) refers to basicmathfunctions.o(i.arm_scale_f64) for arm_scale_f64
    matrixfunctions.o(i.arm_mat_cholesky_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_ldlt_f32) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_mat_ldlt_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to memcpya.o(.text) for __aeabi_memcpy4
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_mult_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_mult_f64) refers to dadd.o(.text) for __aeabi_dadd
    matrixfunctions.o(i.arm_mat_mult_q15) refers to matrixfunctions.o(i.arm_mat_trans_q15) for arm_mat_trans_q15
    matrixfunctions.o(i.arm_mat_qr_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    matrixfunctions.o(i.arm_mat_qr_f32) refers to matrixfunctions.o(i.arm_householder_f32) for arm_householder_f32
    matrixfunctions.o(i.arm_mat_qr_f32) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_mat_qr_f64) refers to memcpya.o(.text) for __aeabi_memcpy4
    matrixfunctions.o(i.arm_mat_qr_f64) refers to matrixfunctions.o(i.arm_householder_f64) for arm_householder_f64
    matrixfunctions.o(i.arm_mat_qr_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_qr_f64) refers to dadd.o(.text) for __aeabi_dadd
    matrixfunctions.o(i.arm_mat_qr_f64) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_sub_f64) refers to dadd.o(.text) for __aeabi_dsub
    quaternionmathfunctions.o(i.arm_quaternion_norm_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    quaternionmathfunctions.o(i.arm_quaternion_normalize_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    quaternionmathfunctions.o(i.arm_quaternion_product_f32) refers to quaternionmathfunctions.o(i.arm_quaternion_product_single_f32) for arm_quaternion_product_single_f32
    quaternionmathfunctions.o(i.arm_rotation2quaternion_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    svmfunctions.o(i.arm_svm_rbf_predict_f32) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    svmfunctions.o(i.arm_svm_sigmoid_predict_f32) refers to tanhf.o(i.__hardfp_tanhf) for __hardfp_tanhf
    statisticsfunctions.o(i.arm_absmax_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    statisticsfunctions.o(i.arm_absmax_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    statisticsfunctions.o(i.arm_absmax_no_idx_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    statisticsfunctions.o(i.arm_absmax_no_idx_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    statisticsfunctions.o(i.arm_absmin_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    statisticsfunctions.o(i.arm_absmin_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    statisticsfunctions.o(i.arm_absmin_no_idx_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    statisticsfunctions.o(i.arm_absmin_no_idx_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    statisticsfunctions.o(i.arm_accumulate_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_entropy_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    statisticsfunctions.o(i.arm_entropy_f64) refers to log.o(i.__hardfp_log) for __hardfp_log
    statisticsfunctions.o(i.arm_entropy_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_entropy_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_kullback_leibler_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    statisticsfunctions.o(i.arm_kullback_leibler_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    statisticsfunctions.o(i.arm_kullback_leibler_f64) refers to log.o(i.__hardfp_log) for __hardfp_log
    statisticsfunctions.o(i.arm_kullback_leibler_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_kullback_leibler_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_logsumexp_dot_prod_f32) refers to basicmathfunctions.o(i.arm_add_f32) for arm_add_f32
    statisticsfunctions.o(i.arm_logsumexp_dot_prod_f32) refers to statisticsfunctions.o(i.arm_logsumexp_f32) for arm_logsumexp_f32
    statisticsfunctions.o(i.arm_logsumexp_f32) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    statisticsfunctions.o(i.arm_logsumexp_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    statisticsfunctions.o(i.arm_max_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    statisticsfunctions.o(i.arm_max_no_idx_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    statisticsfunctions.o(i.arm_mean_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_mean_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    statisticsfunctions.o(i.arm_mean_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    statisticsfunctions.o(i.arm_mean_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_min_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    statisticsfunctions.o(i.arm_min_no_idx_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    statisticsfunctions.o(i.arm_mse_f64) refers to dadd.o(.text) for __aeabi_dsub
    statisticsfunctions.o(i.arm_mse_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_mse_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    statisticsfunctions.o(i.arm_mse_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    statisticsfunctions.o(i.arm_mse_q15) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_mse_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_power_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_power_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_rms_q15) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_rms_q15) refers to fastmathfunctions.o(i.arm_sqrt_q15) for arm_sqrt_q15
    statisticsfunctions.o(i.arm_rms_q31) refers to uldiv.o(.text) for __aeabi_uldivmod
    statisticsfunctions.o(i.arm_rms_q31) refers to fastmathfunctions.o(i.arm_sqrt_q31) for arm_sqrt_q31
    statisticsfunctions.o(i.arm_std_f32) refers to statisticsfunctions.o(i.arm_var_f32) for arm_var_f32
    statisticsfunctions.o(i.arm_std_f64) refers to statisticsfunctions.o(i.arm_var_f64) for arm_var_f64
    statisticsfunctions.o(i.arm_std_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    statisticsfunctions.o(i.arm_std_q15) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_std_q15) refers to fastmathfunctions.o(i.arm_sqrt_q15) for arm_sqrt_q15
    statisticsfunctions.o(i.arm_std_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_std_q31) refers to fastmathfunctions.o(i.arm_sqrt_q31) for arm_sqrt_q31
    statisticsfunctions.o(i.arm_var_f64) refers to statisticsfunctions.o(i.arm_mean_f64) for arm_mean_f64
    statisticsfunctions.o(i.arm_var_f64) refers to dadd.o(.text) for __aeabi_dsub
    statisticsfunctions.o(i.arm_var_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_var_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    statisticsfunctions.o(i.arm_var_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    statisticsfunctions.o(i.arm_var_q15) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_var_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    supportfunctions.o(i.arm_bitonic_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_bitonic_sort_f32) refers to supportfunctions.o(i.arm_bitonic_sort_core_f32) for arm_bitonic_sort_core_f32
    supportfunctions.o(i.arm_bubble_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_f64_to_float) refers to d2f.o(.text) for __aeabi_d2f
    supportfunctions.o(i.arm_f64_to_q15) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_f64_to_q15) refers to dfixi.o(.text) for __aeabi_d2iz
    supportfunctions.o(i.arm_f64_to_q31) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_f64_to_q31) refers to dfixl.o(.text) for __aeabi_d2lz
    supportfunctions.o(i.arm_f64_to_q7) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_f64_to_q7) refers to dfixi.o(.text) for __aeabi_d2iz
    supportfunctions.o(i.arm_float_to_f64) refers to f2d.o(.text) for __aeabi_f2d
    supportfunctions.o(i.arm_float_to_q31) refers to ffixl.o(.text) for __aeabi_f2lz
    supportfunctions.o(i.arm_heap_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_heap_sort_f32) refers to supportfunctions.o(i.arm_heapify) for arm_heapify
    supportfunctions.o(i.arm_insertion_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_merge_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_merge_sort_f32) refers to supportfunctions.o(i.arm_merge_sort_core_f32) for arm_merge_sort_core_f32
    supportfunctions.o(i.arm_q15_to_f64) refers to dflti.o(.text) for __aeabi_i2d
    supportfunctions.o(i.arm_q15_to_f64) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_q31_to_f64) refers to dflti.o(.text) for __aeabi_i2d
    supportfunctions.o(i.arm_q31_to_f64) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_q7_to_f64) refers to dflti.o(.text) for __aeabi_i2d
    supportfunctions.o(i.arm_q7_to_f64) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_quick_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_quick_sort_f32) refers to supportfunctions.o(i.arm_quick_sort_core_f32) for arm_quick_sort_core_f32
    supportfunctions.o(i.arm_selection_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_bitonic_sort_f32) for arm_bitonic_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_bubble_sort_f32) for arm_bubble_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_heap_sort_f32) for arm_heap_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_insertion_sort_f32) for arm_insertion_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_quick_sort_f32) for arm_quick_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_selection_sort_f32) for arm_selection_sort_f32
    transformfunctions.o(i.arm_cfft_f32) refers to transformfunctions.o(i.arm_cfft_radix8by2_f32) for arm_cfft_radix8by2_f32
    transformfunctions.o(i.arm_cfft_f32) refers to transformfunctions.o(i.arm_cfft_radix8by4_f32) for arm_cfft_radix8by4_f32
    transformfunctions.o(i.arm_cfft_f32) refers to transformfunctions.o(i.arm_radix8_butterfly_f32) for arm_radix8_butterfly_f32
    transformfunctions.o(i.arm_cfft_f32) refers to transformfunctions.o(i.arm_bitreversal_32) for arm_bitreversal_32
    transformfunctions.o(i.arm_cfft_f64) refers to transformfunctions.o(i.arm_radix4_butterfly_f64) for arm_radix4_butterfly_f64
    transformfunctions.o(i.arm_cfft_f64) refers to transformfunctions.o(i.arm_cfft_radix4by2_f64) for arm_cfft_radix4by2_f64
    transformfunctions.o(i.arm_cfft_f64) refers to transformfunctions.o(i.arm_bitreversal_64) for arm_bitreversal_64
    transformfunctions.o(i.arm_cfft_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    transformfunctions.o(i.arm_cfft_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    transformfunctions.o(i.arm_cfft_f64) refers to dmul.o(.text) for __aeabi_dmul
    transformfunctions.o(i.arm_cfft_init_1024_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len1024
    transformfunctions.o(i.arm_cfft_init_1024_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len1024
    transformfunctions.o(i.arm_cfft_init_1024_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len1024
    transformfunctions.o(i.arm_cfft_init_1024_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len1024
    transformfunctions.o(i.arm_cfft_init_128_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len128
    transformfunctions.o(i.arm_cfft_init_128_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len128
    transformfunctions.o(i.arm_cfft_init_128_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len128
    transformfunctions.o(i.arm_cfft_init_128_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len128
    transformfunctions.o(i.arm_cfft_init_16_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len16
    transformfunctions.o(i.arm_cfft_init_16_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len16
    transformfunctions.o(i.arm_cfft_init_16_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len16
    transformfunctions.o(i.arm_cfft_init_16_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len16
    transformfunctions.o(i.arm_cfft_init_2048_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len2048
    transformfunctions.o(i.arm_cfft_init_2048_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len2048
    transformfunctions.o(i.arm_cfft_init_2048_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len2048
    transformfunctions.o(i.arm_cfft_init_2048_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len2048
    transformfunctions.o(i.arm_cfft_init_256_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len256
    transformfunctions.o(i.arm_cfft_init_256_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len256
    transformfunctions.o(i.arm_cfft_init_256_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len256
    transformfunctions.o(i.arm_cfft_init_256_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len256
    transformfunctions.o(i.arm_cfft_init_32_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len32
    transformfunctions.o(i.arm_cfft_init_32_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len32
    transformfunctions.o(i.arm_cfft_init_32_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len32
    transformfunctions.o(i.arm_cfft_init_32_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len32
    transformfunctions.o(i.arm_cfft_init_4096_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len4096
    transformfunctions.o(i.arm_cfft_init_4096_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len4096
    transformfunctions.o(i.arm_cfft_init_4096_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len4096
    transformfunctions.o(i.arm_cfft_init_4096_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len4096
    transformfunctions.o(i.arm_cfft_init_512_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len512
    transformfunctions.o(i.arm_cfft_init_512_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len512
    transformfunctions.o(i.arm_cfft_init_512_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len512
    transformfunctions.o(i.arm_cfft_init_512_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len512
    transformfunctions.o(i.arm_cfft_init_64_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len64
    transformfunctions.o(i.arm_cfft_init_64_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len64
    transformfunctions.o(i.arm_cfft_init_64_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len64
    transformfunctions.o(i.arm_cfft_init_64_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len64
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_4096_f32) for arm_cfft_init_4096_f32
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_2048_f32) for arm_cfft_init_2048_f32
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_1024_f32) for arm_cfft_init_1024_f32
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_512_f32) for arm_cfft_init_512_f32
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_256_f32) for arm_cfft_init_256_f32
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_128_f32) for arm_cfft_init_128_f32
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_64_f32) for arm_cfft_init_64_f32
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_32_f32) for arm_cfft_init_32_f32
    transformfunctions.o(i.arm_cfft_init_f32) refers to transformfunctions.o(i.arm_cfft_init_16_f32) for arm_cfft_init_16_f32
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_4096_f64) for arm_cfft_init_4096_f64
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_2048_f64) for arm_cfft_init_2048_f64
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_1024_f64) for arm_cfft_init_1024_f64
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_512_f64) for arm_cfft_init_512_f64
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_256_f64) for arm_cfft_init_256_f64
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_128_f64) for arm_cfft_init_128_f64
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_64_f64) for arm_cfft_init_64_f64
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_32_f64) for arm_cfft_init_32_f64
    transformfunctions.o(i.arm_cfft_init_f64) refers to transformfunctions.o(i.arm_cfft_init_16_f64) for arm_cfft_init_16_f64
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_4096_q15) for arm_cfft_init_4096_q15
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_2048_q15) for arm_cfft_init_2048_q15
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_1024_q15) for arm_cfft_init_1024_q15
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_512_q15) for arm_cfft_init_512_q15
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_256_q15) for arm_cfft_init_256_q15
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_128_q15) for arm_cfft_init_128_q15
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_64_q15) for arm_cfft_init_64_q15
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_32_q15) for arm_cfft_init_32_q15
    transformfunctions.o(i.arm_cfft_init_q15) refers to transformfunctions.o(i.arm_cfft_init_16_q15) for arm_cfft_init_16_q15
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_4096_q31) for arm_cfft_init_4096_q31
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_2048_q31) for arm_cfft_init_2048_q31
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_1024_q31) for arm_cfft_init_1024_q31
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_512_q31) for arm_cfft_init_512_q31
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_256_q31) for arm_cfft_init_256_q31
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_128_q31) for arm_cfft_init_128_q31
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_64_q31) for arm_cfft_init_64_q31
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_32_q31) for arm_cfft_init_32_q31
    transformfunctions.o(i.arm_cfft_init_q31) refers to transformfunctions.o(i.arm_cfft_init_16_q31) for arm_cfft_init_16_q31
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q15) for arm_radix4_butterfly_inverse_q15
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_cfft_radix4by2_inverse_q15) for arm_cfft_radix4by2_inverse_q15
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_q15) for arm_radix4_butterfly_q15
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_cfft_radix4by2_q15) for arm_cfft_radix4by2_q15
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_bitreversal_16) for arm_bitreversal_16
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q31) for arm_radix4_butterfly_inverse_q31
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_cfft_radix4by2_inverse_q31) for arm_cfft_radix4by2_inverse_q31
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_q31) for arm_radix4_butterfly_q31
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_cfft_radix4by2_q31) for arm_cfft_radix4by2_q31
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_bitreversal_32) for arm_bitreversal_32
    transformfunctions.o(i.arm_cfft_radix2_f32) refers to transformfunctions.o(i.arm_radix2_butterfly_f32) for arm_radix2_butterfly_f32
    transformfunctions.o(i.arm_cfft_radix2_f32) refers to transformfunctions.o(i.arm_bitreversal_f32) for arm_bitreversal_f32
    transformfunctions.o(i.arm_cfft_radix2_f32) refers to transformfunctions.o(i.arm_radix2_butterfly_inverse_f32) for arm_radix2_butterfly_inverse_f32
    transformfunctions.o(i.arm_cfft_radix2_init_f32) refers to commontables.o(.constdata) for twiddleCoef_4096
    transformfunctions.o(i.arm_cfft_radix2_init_f32) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix2_init_q15) refers to commontables.o(.constdata) for twiddleCoef_4096_q15
    transformfunctions.o(i.arm_cfft_radix2_init_q15) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix2_init_q31) refers to commontables.o(.constdata) for twiddleCoef_4096_q31
    transformfunctions.o(i.arm_cfft_radix2_init_q31) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix2_q15) refers to transformfunctions.o(i.arm_radix2_butterfly_q15) for arm_radix2_butterfly_q15
    transformfunctions.o(i.arm_cfft_radix2_q15) refers to transformfunctions.o(i.arm_bitreversal_q15) for arm_bitreversal_q15
    transformfunctions.o(i.arm_cfft_radix2_q15) refers to transformfunctions.o(i.arm_radix2_butterfly_inverse_q15) for arm_radix2_butterfly_inverse_q15
    transformfunctions.o(i.arm_cfft_radix2_q31) refers to transformfunctions.o(i.arm_radix2_butterfly_q31) for arm_radix2_butterfly_q31
    transformfunctions.o(i.arm_cfft_radix2_q31) refers to transformfunctions.o(i.arm_bitreversal_q31) for arm_bitreversal_q31
    transformfunctions.o(i.arm_cfft_radix2_q31) refers to transformfunctions.o(i.arm_radix2_butterfly_inverse_q31) for arm_radix2_butterfly_inverse_q31
    transformfunctions.o(i.arm_cfft_radix4_f32) refers to transformfunctions.o(i.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    transformfunctions.o(i.arm_cfft_radix4_f32) refers to transformfunctions.o(i.arm_bitreversal_f32) for arm_bitreversal_f32
    transformfunctions.o(i.arm_cfft_radix4_f32) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    transformfunctions.o(i.arm_cfft_radix4_init_f32) refers to commontables.o(.constdata) for twiddleCoef_4096
    transformfunctions.o(i.arm_cfft_radix4_init_f32) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix4_init_q15) refers to commontables.o(.constdata) for twiddleCoef_4096_q15
    transformfunctions.o(i.arm_cfft_radix4_init_q15) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix4_init_q31) refers to commontables.o(.constdata) for twiddleCoef_4096_q31
    transformfunctions.o(i.arm_cfft_radix4_init_q31) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix4_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_q15) for arm_radix4_butterfly_q15
    transformfunctions.o(i.arm_cfft_radix4_q15) refers to transformfunctions.o(i.arm_bitreversal_q15) for arm_bitreversal_q15
    transformfunctions.o(i.arm_cfft_radix4_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q15) for arm_radix4_butterfly_inverse_q15
    transformfunctions.o(i.arm_cfft_radix4_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_q31) for arm_radix4_butterfly_q31
    transformfunctions.o(i.arm_cfft_radix4_q31) refers to transformfunctions.o(i.arm_bitreversal_q31) for arm_bitreversal_q31
    transformfunctions.o(i.arm_cfft_radix4_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q31) for arm_radix4_butterfly_inverse_q31
    transformfunctions.o(i.arm_cfft_radix4by2_f64) refers to dadd.o(.text) for __aeabi_dadd
    transformfunctions.o(i.arm_cfft_radix4by2_f64) refers to dmul.o(.text) for __aeabi_dmul
    transformfunctions.o(i.arm_cfft_radix4by2_f64) refers to transformfunctions.o(i.arm_radix4_butterfly_f64) for arm_radix4_butterfly_f64
    transformfunctions.o(i.arm_cfft_radix4by2_inverse_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q15) for arm_radix4_butterfly_inverse_q15
    transformfunctions.o(i.arm_cfft_radix4by2_inverse_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q31) for arm_radix4_butterfly_inverse_q31
    transformfunctions.o(i.arm_cfft_radix4by2_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_q15) for arm_radix4_butterfly_q15
    transformfunctions.o(i.arm_cfft_radix4by2_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_q31) for arm_radix4_butterfly_q31
    transformfunctions.o(i.arm_cfft_radix8by2_f32) refers to transformfunctions.o(i.arm_radix8_butterfly_f32) for arm_radix8_butterfly_f32
    transformfunctions.o(i.arm_cfft_radix8by4_f32) refers to transformfunctions.o(i.arm_radix8_butterfly_f32) for arm_radix8_butterfly_f32
    transformfunctions.o(i.arm_dct4_f32) refers to basicmathfunctions.o(i.arm_scale_f32) for arm_scale_f32
    transformfunctions.o(i.arm_dct4_f32) refers to basicmathfunctions.o(i.arm_mult_f32) for arm_mult_f32
    transformfunctions.o(i.arm_dct4_f32) refers to transformfunctions.o(i.arm_rfft_f32) for arm_rfft_f32
    transformfunctions.o(i.arm_dct4_f32) refers to complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f32) for arm_cmplx_mult_cmplx_f32
    transformfunctions.o(i.arm_dct4_init_f32) refers to transformfunctions.o(i.arm_rfft_init_f32) for arm_rfft_init_f32
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for Weights_8192
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for cos_factors_8192
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for Weights_2048
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for cos_factors_2048
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for Weights_512
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for cos_factors_512
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for Weights_128
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for cos_factors_128
    transformfunctions.o(i.arm_dct4_init_q15) refers to transformfunctions.o(i.arm_rfft_init_q15) for arm_rfft_init_q15
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for WeightsQ15_8192
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for cos_factorsQ15_8192
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for WeightsQ15_2048
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for cos_factorsQ15_2048
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for WeightsQ15_512
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for cos_factorsQ15_512
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for WeightsQ15_128
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for cos_factorsQ15_128
    transformfunctions.o(i.arm_dct4_init_q31) refers to transformfunctions.o(i.arm_rfft_init_q31) for arm_rfft_init_q31
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for WeightsQ31_8192
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for cos_factorsQ31_8192
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for WeightsQ31_2048
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for cos_factorsQ31_2048
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for WeightsQ31_512
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for cos_factorsQ31_512
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for WeightsQ31_128
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for cos_factorsQ31_128
    transformfunctions.o(i.arm_dct4_q15) refers to basicmathfunctions.o(i.arm_mult_q15) for arm_mult_q15
    transformfunctions.o(i.arm_dct4_q15) refers to basicmathfunctions.o(i.arm_shift_q15) for arm_shift_q15
    transformfunctions.o(i.arm_dct4_q15) refers to transformfunctions.o(i.arm_rfft_q15) for arm_rfft_q15
    transformfunctions.o(i.arm_dct4_q15) refers to complexmathfunctions.o(i.arm_cmplx_mult_cmplx_q15) for arm_cmplx_mult_cmplx_q15
    transformfunctions.o(i.arm_dct4_q31) refers to basicmathfunctions.o(i.arm_mult_q31) for arm_mult_q31
    transformfunctions.o(i.arm_dct4_q31) refers to basicmathfunctions.o(i.arm_shift_q31) for arm_shift_q31
    transformfunctions.o(i.arm_dct4_q31) refers to transformfunctions.o(i.arm_rfft_q31) for arm_rfft_q31
    transformfunctions.o(i.arm_dct4_q31) refers to complexmathfunctions.o(i.arm_cmplx_mult_cmplx_q31) for arm_cmplx_mult_cmplx_q31
    transformfunctions.o(i.arm_mfcc_f32) refers to statisticsfunctions.o(i.arm_absmax_f32) for arm_absmax_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to basicmathfunctions.o(i.arm_scale_f32) for arm_scale_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to basicmathfunctions.o(i.arm_mult_f32) for arm_mult_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to transformfunctions.o(i.arm_rfft_fast_f32) for arm_rfft_fast_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to complexmathfunctions.o(i.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to basicmathfunctions.o(i.arm_dot_prod_f32) for arm_dot_prod_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to basicmathfunctions.o(i.arm_offset_f32) for arm_offset_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to fastmathfunctions.o(i.arm_vlog_f32) for arm_vlog_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to matrixfunctions.o(i.arm_mat_vec_mult_f32) for arm_mat_vec_mult_f32
    transformfunctions.o(i.arm_mfcc_init_1024_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_1024_f32) for arm_rfft_fast_init_1024_f32
    transformfunctions.o(i.arm_mfcc_init_1024_q15) refers to transformfunctions.o(i.arm_rfft_init_1024_q15) for arm_rfft_init_1024_q15
    transformfunctions.o(i.arm_mfcc_init_1024_q31) refers to transformfunctions.o(i.arm_rfft_init_1024_q31) for arm_rfft_init_1024_q31
    transformfunctions.o(i.arm_mfcc_init_128_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_128_f32) for arm_rfft_fast_init_128_f32
    transformfunctions.o(i.arm_mfcc_init_128_q15) refers to transformfunctions.o(i.arm_rfft_init_128_q15) for arm_rfft_init_128_q15
    transformfunctions.o(i.arm_mfcc_init_128_q31) refers to transformfunctions.o(i.arm_rfft_init_128_q31) for arm_rfft_init_128_q31
    transformfunctions.o(i.arm_mfcc_init_2048_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_2048_f32) for arm_rfft_fast_init_2048_f32
    transformfunctions.o(i.arm_mfcc_init_2048_q15) refers to transformfunctions.o(i.arm_rfft_init_2048_q15) for arm_rfft_init_2048_q15
    transformfunctions.o(i.arm_mfcc_init_2048_q31) refers to transformfunctions.o(i.arm_rfft_init_2048_q31) for arm_rfft_init_2048_q31
    transformfunctions.o(i.arm_mfcc_init_256_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_256_f32) for arm_rfft_fast_init_256_f32
    transformfunctions.o(i.arm_mfcc_init_256_q15) refers to transformfunctions.o(i.arm_rfft_init_256_q15) for arm_rfft_init_256_q15
    transformfunctions.o(i.arm_mfcc_init_256_q31) refers to transformfunctions.o(i.arm_rfft_init_256_q31) for arm_rfft_init_256_q31
    transformfunctions.o(i.arm_mfcc_init_32_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_32_f32) for arm_rfft_fast_init_32_f32
    transformfunctions.o(i.arm_mfcc_init_32_q15) refers to transformfunctions.o(i.arm_rfft_init_32_q15) for arm_rfft_init_32_q15
    transformfunctions.o(i.arm_mfcc_init_32_q31) refers to transformfunctions.o(i.arm_rfft_init_32_q31) for arm_rfft_init_32_q31
    transformfunctions.o(i.arm_mfcc_init_4096_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_4096_f32) for arm_rfft_fast_init_4096_f32
    transformfunctions.o(i.arm_mfcc_init_4096_q15) refers to transformfunctions.o(i.arm_rfft_init_4096_q15) for arm_rfft_init_4096_q15
    transformfunctions.o(i.arm_mfcc_init_4096_q31) refers to transformfunctions.o(i.arm_rfft_init_4096_q31) for arm_rfft_init_4096_q31
    transformfunctions.o(i.arm_mfcc_init_512_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_512_f32) for arm_rfft_fast_init_512_f32
    transformfunctions.o(i.arm_mfcc_init_512_q15) refers to transformfunctions.o(i.arm_rfft_init_512_q15) for arm_rfft_init_512_q15
    transformfunctions.o(i.arm_mfcc_init_512_q31) refers to transformfunctions.o(i.arm_rfft_init_512_q31) for arm_rfft_init_512_q31
    transformfunctions.o(i.arm_mfcc_init_64_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_64_f32) for arm_rfft_fast_init_64_f32
    transformfunctions.o(i.arm_mfcc_init_64_q15) refers to transformfunctions.o(i.arm_rfft_init_64_q15) for arm_rfft_init_64_q15
    transformfunctions.o(i.arm_mfcc_init_64_q31) refers to transformfunctions.o(i.arm_rfft_init_64_q31) for arm_rfft_init_64_q31
    transformfunctions.o(i.arm_mfcc_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_f32) for arm_rfft_fast_init_f32
    transformfunctions.o(i.arm_mfcc_init_q15) refers to transformfunctions.o(i.arm_rfft_init_q15) for arm_rfft_init_q15
    transformfunctions.o(i.arm_mfcc_init_q31) refers to transformfunctions.o(i.arm_rfft_init_q31) for arm_rfft_init_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to statisticsfunctions.o(i.arm_absmax_q15) for arm_absmax_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to fastmathfunctions.o(i.arm_divide_q15) for arm_divide_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_scale_q15) for arm_scale_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_mult_q15) for arm_mult_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to transformfunctions.o(i.arm_rfft_q15) for arm_rfft_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to complexmathfunctions.o(i.arm_cmplx_mag_q15) for arm_cmplx_mag_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_dot_prod_q15) for arm_dot_prod_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_scale_q31) for arm_scale_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to fastmathfunctions.o(i.arm_vlog_q31) for arm_vlog_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_offset_q31) for arm_offset_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_shift_q31) for arm_shift_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to matrixfunctions.o(i.arm_mat_vec_mult_q15) for arm_mat_vec_mult_q15
    transformfunctions.o(i.arm_mfcc_q31) refers to statisticsfunctions.o(i.arm_absmax_q31) for arm_absmax_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to fastmathfunctions.o(i.arm_divide_q31) for arm_divide_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_scale_q31) for arm_scale_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_mult_q31) for arm_mult_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to transformfunctions.o(i.arm_rfft_q31) for arm_rfft_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to complexmathfunctions.o(i.arm_cmplx_mag_q31) for arm_cmplx_mag_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_dot_prod_q31) for arm_dot_prod_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to fastmathfunctions.o(i.arm_vlog_q31) for arm_vlog_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_offset_q31) for arm_offset_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_shift_q31) for arm_shift_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to matrixfunctions.o(i.arm_mat_vec_mult_q31) for arm_mat_vec_mult_q31
    transformfunctions.o(i.arm_radix4_butterfly_f64) refers to dadd.o(.text) for __aeabi_dadd
    transformfunctions.o(i.arm_radix4_butterfly_f64) refers to dmul.o(.text) for __aeabi_dmul
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_split_rifft_f32) for arm_split_rifft_f32
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_bitreversal_f32) for arm_bitreversal_f32
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_split_rfft_f32) for arm_split_rfft_f32
    transformfunctions.o(i.arm_rfft_fast_f32) refers to transformfunctions.o(i.merge_rfft_f32) for merge_rfft_f32
    transformfunctions.o(i.arm_rfft_fast_f32) refers to transformfunctions.o(i.arm_cfft_f32) for arm_cfft_f32
    transformfunctions.o(i.arm_rfft_fast_f32) refers to transformfunctions.o(i.stage_rfft_f32) for stage_rfft_f32
    transformfunctions.o(i.arm_rfft_fast_f64) refers to transformfunctions.o(i.merge_rfft_f64) for merge_rfft_f64
    transformfunctions.o(i.arm_rfft_fast_f64) refers to transformfunctions.o(i.arm_cfft_f64) for arm_cfft_f64
    transformfunctions.o(i.arm_rfft_fast_f64) refers to transformfunctions.o(i.stage_rfft_f64) for stage_rfft_f64
    transformfunctions.o(i.arm_rfft_fast_init_1024_f32) refers to transformfunctions.o(i.arm_cfft_init_512_f32) for arm_cfft_init_512_f32
    transformfunctions.o(i.arm_rfft_fast_init_1024_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_1024
    transformfunctions.o(i.arm_rfft_fast_init_1024_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_512
    transformfunctions.o(i.arm_rfft_fast_init_1024_f64) refers to commontables.o(.constdata) for twiddleCoefF64_512
    transformfunctions.o(i.arm_rfft_fast_init_1024_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_1024
    transformfunctions.o(i.arm_rfft_fast_init_128_f32) refers to transformfunctions.o(i.arm_cfft_init_64_f32) for arm_cfft_init_64_f32
    transformfunctions.o(i.arm_rfft_fast_init_128_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_128
    transformfunctions.o(i.arm_rfft_fast_init_128_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_64
    transformfunctions.o(i.arm_rfft_fast_init_128_f64) refers to commontables.o(.constdata) for twiddleCoefF64_64
    transformfunctions.o(i.arm_rfft_fast_init_128_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_128
    transformfunctions.o(i.arm_rfft_fast_init_2048_f32) refers to transformfunctions.o(i.arm_cfft_init_1024_f32) for arm_cfft_init_1024_f32
    transformfunctions.o(i.arm_rfft_fast_init_2048_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_2048
    transformfunctions.o(i.arm_rfft_fast_init_2048_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_1024
    transformfunctions.o(i.arm_rfft_fast_init_2048_f64) refers to commontables.o(.constdata) for twiddleCoefF64_1024
    transformfunctions.o(i.arm_rfft_fast_init_2048_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_2048
    transformfunctions.o(i.arm_rfft_fast_init_256_f32) refers to transformfunctions.o(i.arm_cfft_init_128_f32) for arm_cfft_init_128_f32
    transformfunctions.o(i.arm_rfft_fast_init_256_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_256
    transformfunctions.o(i.arm_rfft_fast_init_256_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_128
    transformfunctions.o(i.arm_rfft_fast_init_256_f64) refers to commontables.o(.constdata) for twiddleCoefF64_128
    transformfunctions.o(i.arm_rfft_fast_init_256_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_256
    transformfunctions.o(i.arm_rfft_fast_init_32_f32) refers to transformfunctions.o(i.arm_cfft_init_16_f32) for arm_cfft_init_16_f32
    transformfunctions.o(i.arm_rfft_fast_init_32_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_32
    transformfunctions.o(i.arm_rfft_fast_init_32_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_16
    transformfunctions.o(i.arm_rfft_fast_init_32_f64) refers to commontables.o(.constdata) for twiddleCoefF64_16
    transformfunctions.o(i.arm_rfft_fast_init_32_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_32
    transformfunctions.o(i.arm_rfft_fast_init_4096_f32) refers to transformfunctions.o(i.arm_cfft_init_2048_f32) for arm_cfft_init_2048_f32
    transformfunctions.o(i.arm_rfft_fast_init_4096_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_4096
    transformfunctions.o(i.arm_rfft_fast_init_4096_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_2048
    transformfunctions.o(i.arm_rfft_fast_init_4096_f64) refers to commontables.o(.constdata) for twiddleCoefF64_2048
    transformfunctions.o(i.arm_rfft_fast_init_4096_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_4096
    transformfunctions.o(i.arm_rfft_fast_init_512_f32) refers to transformfunctions.o(i.arm_cfft_init_256_f32) for arm_cfft_init_256_f32
    transformfunctions.o(i.arm_rfft_fast_init_512_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_512
    transformfunctions.o(i.arm_rfft_fast_init_512_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_256
    transformfunctions.o(i.arm_rfft_fast_init_512_f64) refers to commontables.o(.constdata) for twiddleCoefF64_256
    transformfunctions.o(i.arm_rfft_fast_init_512_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_512
    transformfunctions.o(i.arm_rfft_fast_init_64_f32) refers to transformfunctions.o(i.arm_cfft_init_32_f32) for arm_cfft_init_32_f32
    transformfunctions.o(i.arm_rfft_fast_init_64_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_64
    transformfunctions.o(i.arm_rfft_fast_init_64_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_32
    transformfunctions.o(i.arm_rfft_fast_init_64_f64) refers to commontables.o(.constdata) for twiddleCoefF64_32
    transformfunctions.o(i.arm_rfft_fast_init_64_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_64
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_4096_f32) for arm_rfft_fast_init_4096_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_2048_f32) for arm_rfft_fast_init_2048_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_1024_f32) for arm_rfft_fast_init_1024_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_512_f32) for arm_rfft_fast_init_512_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_256_f32) for arm_rfft_fast_init_256_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_128_f32) for arm_rfft_fast_init_128_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_64_f32) for arm_rfft_fast_init_64_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_32_f32) for arm_rfft_fast_init_32_f32
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_fast_init_4096_f64) for arm_rfft_fast_init_4096_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_fast_init_2048_f64) for arm_rfft_fast_init_2048_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_fast_init_1024_f64) for arm_rfft_fast_init_1024_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_fast_init_512_f64) for arm_rfft_fast_init_512_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_fast_init_256_f64) for arm_rfft_fast_init_256_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_fast_init_128_f64) for arm_rfft_fast_init_128_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_fast_init_64_f64) for arm_rfft_fast_init_64_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_fast_init_32_f64) for arm_rfft_fast_init_32_f64
    transformfunctions.o(i.arm_rfft_init_1024_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_1024_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_1024_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len512
    transformfunctions.o(i.arm_rfft_init_1024_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_1024_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_1024_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len512
    transformfunctions.o(i.arm_rfft_init_128_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_128_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_128_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len64
    transformfunctions.o(i.arm_rfft_init_128_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_128_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_128_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len64
    transformfunctions.o(i.arm_rfft_init_2048_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_2048_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_2048_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len1024
    transformfunctions.o(i.arm_rfft_init_2048_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_2048_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_2048_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len1024
    transformfunctions.o(i.arm_rfft_init_256_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_256_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_256_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len128
    transformfunctions.o(i.arm_rfft_init_256_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_256_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_256_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len128
    transformfunctions.o(i.arm_rfft_init_32_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_32_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_32_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len16
    transformfunctions.o(i.arm_rfft_init_32_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_32_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_32_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len16
    transformfunctions.o(i.arm_rfft_init_4096_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_4096_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_4096_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len2048
    transformfunctions.o(i.arm_rfft_init_4096_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_4096_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_4096_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len2048
    transformfunctions.o(i.arm_rfft_init_512_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_512_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_512_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len256
    transformfunctions.o(i.arm_rfft_init_512_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_512_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_512_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len256
    transformfunctions.o(i.arm_rfft_init_64_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_64_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_64_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len32
    transformfunctions.o(i.arm_rfft_init_64_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_64_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_64_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len32
    transformfunctions.o(i.arm_rfft_init_8192_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_8192_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_8192_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len4096
    transformfunctions.o(i.arm_rfft_init_8192_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_8192_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_8192_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len4096
    transformfunctions.o(i.arm_rfft_init_f32) refers to transformfunctions.o(i.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    transformfunctions.o(i.arm_rfft_init_f32) refers to commontables.o(.constdata) for realCoefA
    transformfunctions.o(i.arm_rfft_init_f32) refers to commontables.o(.constdata) for realCoefB
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_256_q15) for arm_rfft_init_256_q15
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_8192_q15) for arm_rfft_init_8192_q15
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_4096_q15) for arm_rfft_init_4096_q15
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_2048_q15) for arm_rfft_init_2048_q15
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_1024_q15) for arm_rfft_init_1024_q15
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_512_q15) for arm_rfft_init_512_q15
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_128_q15) for arm_rfft_init_128_q15
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_64_q15) for arm_rfft_init_64_q15
    transformfunctions.o(i.arm_rfft_init_q15) refers to transformfunctions.o(i.arm_rfft_init_32_q15) for arm_rfft_init_32_q15
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_256_q31) for arm_rfft_init_256_q31
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_8192_q31) for arm_rfft_init_8192_q31
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_4096_q31) for arm_rfft_init_4096_q31
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_2048_q31) for arm_rfft_init_2048_q31
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_1024_q31) for arm_rfft_init_1024_q31
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_512_q31) for arm_rfft_init_512_q31
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_128_q31) for arm_rfft_init_128_q31
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_64_q31) for arm_rfft_init_64_q31
    transformfunctions.o(i.arm_rfft_init_q31) refers to transformfunctions.o(i.arm_rfft_init_32_q31) for arm_rfft_init_32_q31
    transformfunctions.o(i.arm_rfft_q15) refers to transformfunctions.o(i.arm_cfft_q15) for arm_cfft_q15
    transformfunctions.o(i.arm_rfft_q15) refers to transformfunctions.o(i.arm_split_rfft_q15) for arm_split_rfft_q15
    transformfunctions.o(i.arm_rfft_q15) refers to transformfunctions.o(i.arm_split_rifft_q15) for arm_split_rifft_q15
    transformfunctions.o(i.arm_rfft_q15) refers to basicmathfunctions.o(i.arm_shift_q15) for arm_shift_q15
    transformfunctions.o(i.arm_rfft_q31) refers to transformfunctions.o(i.arm_cfft_q31) for arm_cfft_q31
    transformfunctions.o(i.arm_rfft_q31) refers to transformfunctions.o(i.arm_split_rfft_q31) for arm_split_rfft_q31
    transformfunctions.o(i.arm_rfft_q31) refers to transformfunctions.o(i.arm_split_rifft_q31) for arm_split_rifft_q31
    transformfunctions.o(i.arm_rfft_q31) refers to basicmathfunctions.o(i.arm_shift_q31) for arm_shift_q31
    transformfunctions.o(i.merge_rfft_f64) refers to dadd.o(.text) for __aeabi_dadd
    transformfunctions.o(i.merge_rfft_f64) refers to dmul.o(.text) for __aeabi_dmul
    transformfunctions.o(i.stage_rfft_f64) refers to dadd.o(.text) for __aeabi_dadd
    transformfunctions.o(i.stage_rfft_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_bartlett_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_bartlett_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_bartlett_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_bartlett_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    windowfunctions.o(i.arm_bartlett_f64) refers to dadd.o(.text) for __aeabi_dsub
    windowfunctions.o(i.arm_blackman_harris_92db_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_blackman_harris_92db_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_blackman_harris_92db_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_blackman_harris_92db_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_blackman_harris_92db_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_blackman_harris_92db_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hamming_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hamming_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hamming_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hamming_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hamming_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hamming_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hanning_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hanning_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hanning_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hanning_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hanning_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hanning_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hft116d_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hft116d_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hft116d_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hft116d_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hft116d_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hft116d_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hft144d_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hft144d_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hft144d_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hft144d_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hft144d_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hft144d_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hft169d_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hft169d_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hft169d_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hft169d_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hft169d_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hft169d_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hft196d_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hft196d_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hft196d_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hft196d_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hft196d_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hft196d_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hft223d_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hft223d_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hft223d_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hft223d_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hft223d_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hft223d_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hft248d_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hft248d_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hft248d_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hft248d_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hft248d_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hft248d_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hft90d_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hft90d_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hft90d_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hft90d_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hft90d_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hft90d_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_hft95_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_hft95_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_hft95_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_hft95_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_hft95_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_hft95_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_nuttall3_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_nuttall3_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_nuttall3_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_nuttall3_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_nuttall3_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_nuttall3_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_nuttall3a_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_nuttall3a_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_nuttall3a_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_nuttall3a_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_nuttall3a_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_nuttall3a_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_nuttall3b_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_nuttall3b_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_nuttall3b_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_nuttall3b_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_nuttall3b_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_nuttall3b_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_nuttall4_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_nuttall4_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_nuttall4_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_nuttall4_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_nuttall4_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_nuttall4_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_nuttall4a_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_nuttall4a_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_nuttall4a_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_nuttall4a_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_nuttall4a_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_nuttall4a_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_nuttall4b_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_nuttall4b_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_nuttall4b_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_nuttall4b_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_nuttall4b_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_nuttall4b_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_nuttall4c_f32) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    windowfunctions.o(i.arm_nuttall4c_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_nuttall4c_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_nuttall4c_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_nuttall4c_f64) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    windowfunctions.o(i.arm_nuttall4c_f64) refers to dadd.o(.text) for __aeabi_drsub
    windowfunctions.o(i.arm_welch_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    windowfunctions.o(i.arm_welch_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    windowfunctions.o(i.arm_welch_f64) refers to dmul.o(.text) for __aeabi_dmul
    windowfunctions.o(i.arm_welch_f64) refers to dadd.o(.text) for __aeabi_dsub
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    cos.o(i.__hardfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__softfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    exp.o(i.__hardfp_exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp.o(i.__hardfp_exp) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    exp.o(i.__hardfp_exp) refers to errno.o(i.__set_errno) for __set_errno
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    exp.o(i.__hardfp_exp) refers to cdcmple.o(.text) for __aeabi_cdcmple
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    exp.o(i.__hardfp_exp) refers to dadd.o(.text) for __aeabi_dsub
    exp.o(i.__hardfp_exp) refers to dmul.o(.text) for __aeabi_dmul
    exp.o(i.__hardfp_exp) refers to dfixi.o(.text) for __aeabi_d2iz
    exp.o(i.__hardfp_exp) refers to dflti.o(.text) for __aeabi_i2d
    exp.o(i.__hardfp_exp) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp.o(i.__hardfp_exp) refers to ddiv.o(.text) for __aeabi_ddiv
    exp.o(i.__hardfp_exp) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    exp.o(i.__hardfp_exp) refers to exp.o(.constdata) for .constdata
    exp.o(i.__softfp_exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.__softfp_exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(i.exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp_x.o(i.____hardfp_exp$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to errno.o(i.__set_errno) for __set_errno
    exp_x.o(i.____hardfp_exp$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to dadd.o(.text) for __aeabi_dsub
    exp_x.o(i.____hardfp_exp$lsc) refers to dmul.o(.text) for __aeabi_dmul
    exp_x.o(i.____hardfp_exp$lsc) refers to dfixi.o(.text) for __aeabi_d2iz
    exp_x.o(i.____hardfp_exp$lsc) refers to dflti.o(.text) for __aeabi_i2d
    exp_x.o(i.____hardfp_exp$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp_x.o(i.____hardfp_exp$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    exp_x.o(i.____hardfp_exp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    exp_x.o(i.____hardfp_exp$lsc) refers to exp_x.o(.constdata) for .constdata
    exp_x.o(i.____softfp_exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____softfp_exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(i.__exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.__exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf.o(i.__hardfp_expf) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf.o(i.__hardfp_expf) refers to errno.o(i.__set_errno) for __set_errno
    expf.o(i.__hardfp_expf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    expf.o(i.__hardfp_expf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    expf.o(i.__hardfp_expf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    expf.o(i.__hardfp_expf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    expf.o(i.__hardfp_expf) refers to expf.o(.constdata) for .constdata
    expf.o(i.__softfp_expf) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf.o(i.__softfp_expf) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    expf.o(i.expf) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf.o(i.expf) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    expf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf_x.o(i.____hardfp_expf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf_x.o(i.____hardfp_expf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    expf_x.o(i.____hardfp_expf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    expf_x.o(i.____hardfp_expf$lsc) refers to fpstat.o(.text) for __ieee_status
    expf_x.o(i.____hardfp_expf$lsc) refers to expf_x.o(.constdata) for .constdata
    expf_x.o(i.____softfp_expf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf_x.o(i.____softfp_expf$lsc) refers to expf_x.o(i.____hardfp_expf$lsc) for ____hardfp_expf$lsc
    expf_x.o(i.__expf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf_x.o(i.__expf$lsc) refers to expf_x.o(i.____hardfp_expf$lsc) for ____hardfp_expf$lsc
    expf_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__hardfp_log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.__hardfp_log) refers to errno.o(i.__set_errno) for __set_errno
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.__hardfp_log) refers to dmul.o(.text) for __aeabi_dmul
    log.o(i.__hardfp_log) refers to dadd.o(.text) for __aeabi_dsub
    log.o(i.__hardfp_log) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    log.o(i.__hardfp_log) refers to dflti.o(.text) for __aeabi_i2d
    log.o(i.__hardfp_log) refers to ddiv.o(.text) for __aeabi_ddiv
    log.o(i.__hardfp_log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.__hardfp_log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.__hardfp_log) refers to log.o(.constdata) for .constdata
    log.o(i.__softfp_log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(i.log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log_x.o(i.____hardfp_log$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log_x.o(i.____hardfp_log$lsc) refers to dmul.o(.text) for __aeabi_dmul
    log_x.o(i.____hardfp_log$lsc) refers to dadd.o(.text) for __aeabi_dsub
    log_x.o(i.____hardfp_log$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    log_x.o(i.____hardfp_log$lsc) refers to dflti.o(.text) for __aeabi_i2d
    log_x.o(i.____hardfp_log$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    log_x.o(i.____hardfp_log$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log_x.o(i.____hardfp_log$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    log_x.o(i.____hardfp_log$lsc) refers to log_x.o(.constdata) for .constdata
    log_x.o(i.____softfp_log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(i.__log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.__log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf.o(i.__hardfp_logf) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf.o(i.__hardfp_logf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    logf.o(i.__hardfp_logf) refers to errno.o(i.__set_errno) for __set_errno
    logf.o(i.__hardfp_logf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    logf.o(i.__hardfp_logf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    logf.o(i.__hardfp_logf) refers to logf.o(.constdata) for .constdata
    logf.o(i.__softfp_logf) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf.o(i.__softfp_logf) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    logf.o(i.logf) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf.o(i.logf) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    logf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf_x.o(i.____hardfp_logf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf_x.o(i.____hardfp_logf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    logf_x.o(i.____hardfp_logf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    logf_x.o(i.____hardfp_logf$lsc) refers to logf_x.o(.constdata) for .constdata
    logf_x.o(i.____softfp_logf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf_x.o(i.____softfp_logf$lsc) refers to logf_x.o(i.____hardfp_logf$lsc) for ____hardfp_logf$lsc
    logf_x.o(i.__logf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf_x.o(i.__logf$lsc) refers to logf_x.o(i.____hardfp_logf$lsc) for ____hardfp_logf$lsc
    logf_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__hardfp_powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__hardfp_powf) refers to fpstat.o(.text) for __ieee_status
    powf.o(i.__hardfp_powf) refers to errno.o(i.__set_errno) for __set_errno
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    powf.o(i.__hardfp_powf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    powf.o(i.__hardfp_powf) refers to powf.o(.constdata) for .constdata
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    powf.o(i.__softfp_powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__softfp_powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(i.powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers to fpstat.o(.text) for __ieee_status
    powf_x.o(i.____hardfp_powf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    powf_x.o(i.____hardfp_powf$lsc) refers to powf_x.o(.constdata) for .constdata
    powf_x.o(i.____hardfp_powf$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf_x.o(i.____softfp_powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____softfp_powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(i.__powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.__powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    tanhf.o(i.__hardfp_tanhf) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf.o(i.__hardfp_tanhf) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    tanhf.o(i.__hardfp_tanhf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    tanhf.o(i.__hardfp_tanhf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    tanhf.o(i.__hardfp_tanhf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    tanhf.o(i.__softfp_tanhf) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf.o(i.__softfp_tanhf) refers to tanhf.o(i.__hardfp_tanhf) for __hardfp_tanhf
    tanhf.o(i.tanhf) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf.o(i.tanhf) refers to tanhf.o(i.__hardfp_tanhf) for __hardfp_tanhf
    tanhf_x.o(i.____hardfp_tanhf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf_x.o(i.____hardfp_tanhf$lsc) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    tanhf_x.o(i.____hardfp_tanhf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    tanhf_x.o(i.____softfp_tanhf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf_x.o(i.____softfp_tanhf$lsc) refers to tanhf_x.o(i.____hardfp_tanhf$lsc) for ____hardfp_tanhf$lsc
    tanhf_x.o(i.__tanhf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf_x.o(i.__tanhf$lsc) refers to tanhf_x.o(i.____hardfp_tanhf$lsc) for ____hardfp_tanhf$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixl.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixl.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixl.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixl.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixl.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to dadd.o(.text) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (60 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (28 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (64 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_AttributeSpace_Timing_Init), (36 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_CommonSpace_Timing_Init), (36 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC), (80 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_Init), (72 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit), (50 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init), (60 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init), (104 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init), (60 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_AttributeSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_CommonSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_IOSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_Init), (40 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (28 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Init), (92 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (88 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (66 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (70 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (92 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (58 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (94 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (62 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (66 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (76 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACplt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMAError), (26 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (112 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (170 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (252 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (444 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (58 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (220 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (228 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (86 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (272 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (96 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.HAL_SRAM_MspInit), (116 bytes).
    Removing lcd.o(i.lcd_clear), (48 bytes).
    Removing lcd.o(i.lcd_color_fill), (88 bytes).
    Removing lcd.o(i.lcd_display_dir), (236 bytes).
    Removing lcd.o(i.lcd_display_off), (32 bytes).
    Removing lcd.o(i.lcd_display_on), (32 bytes).
    Removing lcd.o(i.lcd_draw_circle), (196 bytes).
    Removing lcd.o(i.lcd_draw_hline), (40 bytes).
    Removing lcd.o(i.lcd_draw_line), (156 bytes).
    Removing lcd.o(i.lcd_draw_rectangle), (68 bytes).
    Removing lcd.o(i.lcd_ex_ili9341_reginit), (558 bytes).
    Removing lcd.o(i.lcd_ex_ili9806_reginit), (834 bytes).
    Removing lcd.o(i.lcd_ex_nt35310_reginit), (3828 bytes).
    Removing lcd.o(i.lcd_ex_nt35510_reginit), (3946 bytes).
    Removing lcd.o(i.lcd_ex_ssd1963_reginit), (368 bytes).
    Removing lcd.o(i.lcd_ex_st7789_reginit), (426 bytes).
    Removing lcd.o(i.lcd_ex_st7796_reginit), (456 bytes).
    Removing lcd.o(i.lcd_fill_circle), (182 bytes).
    Removing lcd.o(i.lcd_init), (884 bytes).
    Removing lcd.o(i.lcd_rd_data), (26 bytes).
    Removing lcd.o(i.lcd_read_point), (116 bytes).
    Removing lcd.o(i.lcd_scan_dir), (388 bytes).
    Removing lcd.o(i.lcd_set_window), (344 bytes).
    Removing lcd.o(i.lcd_ssd_backlight_set), (80 bytes).
    Removing lcd.o(i.lcd_write_reg), (12 bytes).
    Removing lcd.o(.data), (4 bytes).
    Removing tlc5615.o(.rev16_text), (4 bytes).
    Removing tlc5615.o(.revsh_text), (4 bytes).
    Removing tlc5615.o(.rrx_text), (6 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing scheduler.o(i.scheduler_run), (60 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(i.led_disp), (76 bytes).
    Removing led_app.o(i.led_task), (12 bytes).
    Removing led_app.o(.data), (3 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing uart_app.o(i.uart_task), (68 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rrx_text), (6 bytes).
    Removing adc_app.o(i.dac_app_get_adc_sampling_interval_us), (12 bytes).
    Removing waveform_analyzer_app.o(.rev16_text), (4 bytes).
    Removing waveform_analyzer_app.o(.revsh_text), (4 bytes).
    Removing waveform_analyzer_app.o(.rrx_text), (6 bytes).
    Removing waveform_analyzer_app.o(i.GetWaveformTypeString), (136 bytes).
    Removing waveform_analyzer_app.o(i.Get_Phase_Difference), (80 bytes).
    Removing waveform_analyzer_app.o(i.Get_Waveform_Frequency), (144 bytes).
    Removing waveform_analyzer_app.o(i.Get_Waveform_Phase_ZeroCrossing), (320 bytes).
    Removing waveform_analyzer_app.o(i.Get_Waveform_Type), (10 bytes).
    Removing waveform_analyzer_app.o(i.Map_FFT_To_Input_Frequency), (192 bytes).
    Removing waveform_analyzer_app.o(i.Perform_FFT), (108 bytes).
    Removing basicmathfunctions.o(.rev16_text), (4 bytes).
    Removing basicmathfunctions.o(.revsh_text), (4 bytes).
    Removing basicmathfunctions.o(.rrx_text), (6 bytes).
    Removing basicmathfunctions.o(i.arm_abs_f32), (24 bytes).
    Removing basicmathfunctions.o(i.arm_abs_f64), (32 bytes).
    Removing basicmathfunctions.o(i.arm_abs_q15), (34 bytes).
    Removing basicmathfunctions.o(i.arm_abs_q31), (26 bytes).
    Removing basicmathfunctions.o(i.arm_abs_q7), (34 bytes).
    Removing basicmathfunctions.o(i.arm_add_f32), (28 bytes).
    Removing basicmathfunctions.o(i.arm_add_f64), (54 bytes).
    Removing basicmathfunctions.o(i.arm_add_q15), (28 bytes).
    Removing basicmathfunctions.o(i.arm_add_q31), (22 bytes).
    Removing basicmathfunctions.o(i.arm_add_q7), (30 bytes).
    Removing basicmathfunctions.o(i.arm_and_u16), (26 bytes).
    Removing basicmathfunctions.o(i.arm_and_u32), (20 bytes).
    Removing basicmathfunctions.o(i.arm_and_u8), (26 bytes).
    Removing basicmathfunctions.o(i.arm_clip_f32), (68 bytes).
    Removing basicmathfunctions.o(i.arm_clip_q15), (44 bytes).
    Removing basicmathfunctions.o(i.arm_clip_q31), (44 bytes).
    Removing basicmathfunctions.o(i.arm_clip_q7), (36 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_f32), (36 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_f64), (88 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_q15), (32 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_q31), (42 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_q7), (30 bytes).
    Removing basicmathfunctions.o(i.arm_mult_f32), (28 bytes).
    Removing basicmathfunctions.o(i.arm_mult_f64), (54 bytes).
    Removing basicmathfunctions.o(i.arm_mult_q15), (32 bytes).
    Removing basicmathfunctions.o(i.arm_mult_q31), (28 bytes).
    Removing basicmathfunctions.o(i.arm_mult_q7), (32 bytes).
    Removing basicmathfunctions.o(i.arm_negate_f32), (24 bytes).
    Removing basicmathfunctions.o(i.arm_negate_f64), (38 bytes).
    Removing basicmathfunctions.o(i.arm_negate_q15), (40 bytes).
    Removing basicmathfunctions.o(i.arm_negate_q31), (22 bytes).
    Removing basicmathfunctions.o(i.arm_negate_q7), (28 bytes).
    Removing basicmathfunctions.o(i.arm_not_u16), (22 bytes).
    Removing basicmathfunctions.o(i.arm_not_u32), (16 bytes).
    Removing basicmathfunctions.o(i.arm_not_u8), (22 bytes).
    Removing basicmathfunctions.o(i.arm_offset_f32), (24 bytes).
    Removing basicmathfunctions.o(i.arm_offset_f64), (58 bytes).
    Removing basicmathfunctions.o(i.arm_offset_q15), (24 bytes).
    Removing basicmathfunctions.o(i.arm_offset_q31), (20 bytes).
    Removing basicmathfunctions.o(i.arm_offset_q7), (26 bytes).
    Removing basicmathfunctions.o(i.arm_or_u16), (26 bytes).
    Removing basicmathfunctions.o(i.arm_or_u32), (20 bytes).
    Removing basicmathfunctions.o(i.arm_or_u8), (26 bytes).
    Removing basicmathfunctions.o(i.arm_scale_f32), (24 bytes).
    Removing basicmathfunctions.o(i.arm_scale_f64), (58 bytes).
    Removing basicmathfunctions.o(i.arm_scale_q15), (38 bytes).
    Removing basicmathfunctions.o(i.arm_scale_q31), (74 bytes).
    Removing basicmathfunctions.o(i.arm_scale_q7), (38 bytes).
    Removing basicmathfunctions.o(i.arm_shift_q15), (50 bytes).
    Removing basicmathfunctions.o(i.arm_shift_q31), (74 bytes).
    Removing basicmathfunctions.o(i.arm_shift_q7), (50 bytes).
    Removing basicmathfunctions.o(i.arm_sub_f32), (28 bytes).
    Removing basicmathfunctions.o(i.arm_sub_f64), (54 bytes).
    Removing basicmathfunctions.o(i.arm_sub_q15), (28 bytes).
    Removing basicmathfunctions.o(i.arm_sub_q31), (22 bytes).
    Removing basicmathfunctions.o(i.arm_sub_q7), (30 bytes).
    Removing basicmathfunctions.o(i.arm_xor_u16), (26 bytes).
    Removing basicmathfunctions.o(i.arm_xor_u32), (20 bytes).
    Removing basicmathfunctions.o(i.arm_xor_u8), (26 bytes).
    Removing basicmathfunctionsf16.o(.rev16_text), (4 bytes).
    Removing basicmathfunctionsf16.o(.revsh_text), (4 bytes).
    Removing basicmathfunctionsf16.o(.rrx_text), (6 bytes).
    Removing bayesfunctions.o(.rev16_text), (4 bytes).
    Removing bayesfunctions.o(.revsh_text), (4 bytes).
    Removing bayesfunctions.o(.rrx_text), (6 bytes).
    Removing bayesfunctions.o(i.arm_gaussian_naive_bayes_predict_f32), (180 bytes).
    Removing bayesfunctionsf16.o(.rev16_text), (4 bytes).
    Removing bayesfunctionsf16.o(.revsh_text), (4 bytes).
    Removing bayesfunctionsf16.o(.rrx_text), (6 bytes).
    Removing commontables.o(.rev16_text), (4 bytes).
    Removing commontables.o(.revsh_text), (4 bytes).
    Removing commontables.o(.rrx_text), (6 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (65536 bytes).
    Removing commontables.o(.constdata), (128 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (96 bytes).
    Removing commontables.o(.constdata), (192 bytes).
    Removing commontables.o(.constdata), (384 bytes).
    Removing commontables.o(.constdata), (768 bytes).
    Removing commontables.o(.constdata), (1536 bytes).
    Removing commontables.o(.constdata), (3072 bytes).
    Removing commontables.o(.constdata), (6144 bytes).
    Removing commontables.o(.constdata), (12288 bytes).
    Removing commontables.o(.constdata), (24576 bytes).
    Removing commontables.o(.constdata), (48 bytes).
    Removing commontables.o(.constdata), (96 bytes).
    Removing commontables.o(.constdata), (192 bytes).
    Removing commontables.o(.constdata), (384 bytes).
    Removing commontables.o(.constdata), (768 bytes).
    Removing commontables.o(.constdata), (1536 bytes).
    Removing commontables.o(.constdata), (3072 bytes).
    Removing commontables.o(.constdata), (6144 bytes).
    Removing commontables.o(.constdata), (12288 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (48 bytes).
    Removing commontables.o(.constdata), (112 bytes).
    Removing commontables.o(.constdata), (224 bytes).
    Removing commontables.o(.constdata), (480 bytes).
    Removing commontables.o(.constdata), (960 bytes).
    Removing commontables.o(.constdata), (1984 bytes).
    Removing commontables.o(.constdata), (3968 bytes).
    Removing commontables.o(.constdata), (8064 bytes).
    Removing commontables.o(.constdata), (40 bytes).
    Removing commontables.o(.constdata), (96 bytes).
    Removing commontables.o(.constdata), (112 bytes).
    Removing commontables.o(.constdata), (416 bytes).
    Removing commontables.o(.constdata), (880 bytes).
    Removing commontables.o(.constdata), (896 bytes).
    Removing commontables.o(.constdata), (3600 bytes).
    Removing commontables.o(.constdata), (7616 bytes).
    Removing commontables.o(.constdata), (8064 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (48 bytes).
    Removing commontables.o(.constdata), (112 bytes).
    Removing commontables.o(.constdata), (224 bytes).
    Removing commontables.o(.constdata), (480 bytes).
    Removing commontables.o(.constdata), (960 bytes).
    Removing commontables.o(.constdata), (1984 bytes).
    Removing commontables.o(.constdata), (3968 bytes).
    Removing commontables.o(.constdata), (8064 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (128 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (65536 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (65536 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (128 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (2052 bytes).
    Removing commontables.o(.constdata), (2052 bytes).
    Removing commontables.o(.constdata), (1026 bytes).
    Removing commontables.o(.constdata), (128 bytes).
    Removing commontables.o(.constdata), (32 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontablesf16.o(.rev16_text), (4 bytes).
    Removing commontablesf16.o(.revsh_text), (4 bytes).
    Removing commontablesf16.o(.rrx_text), (6 bytes).
    Removing complexmathfunctions.o(.rev16_text), (4 bytes).
    Removing complexmathfunctions.o(.revsh_text), (4 bytes).
    Removing complexmathfunctions.o(.rrx_text), (6 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_conj_f32), (36 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_conj_q15), (34 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_conj_q31), (26 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_dot_prod_f32), (72 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_dot_prod_q15), (96 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_dot_prod_q31), (130 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_f64), (86 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_fast_q15), (46 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_q15), (52 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_q31), (46 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_squared_f32), (34 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_squared_f64), (78 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_squared_q15), (32 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_squared_q31), (32 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f32), (58 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f64), (134 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_cmplx_q15), (66 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_cmplx_q31), (70 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_real_f32), (44 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_real_q15), (48 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_real_q31), (42 bytes).
    Removing complexmathfunctionsf16.o(.rev16_text), (4 bytes).
    Removing complexmathfunctionsf16.o(.revsh_text), (4 bytes).
    Removing complexmathfunctionsf16.o(.rrx_text), (6 bytes).
    Removing controllerfunctions.o(.rev16_text), (4 bytes).
    Removing controllerfunctions.o(.revsh_text), (4 bytes).
    Removing controllerfunctions.o(.rrx_text), (6 bytes).
    Removing controllerfunctions.o(i.arm_pid_init_f32), (54 bytes).
    Removing controllerfunctions.o(i.arm_pid_init_q15), (52 bytes).
    Removing controllerfunctions.o(i.arm_pid_init_q31), (46 bytes).
    Removing controllerfunctions.o(i.arm_pid_reset_f32), (10 bytes).
    Removing controllerfunctions.o(i.arm_pid_reset_q15), (8 bytes).
    Removing controllerfunctions.o(i.arm_pid_reset_q31), (10 bytes).
    Removing controllerfunctions.o(i.arm_sin_cos_f32), (272 bytes).
    Removing controllerfunctions.o(i.arm_sin_cos_q31), (528 bytes).
    Removing distancefunctions.o(.rev16_text), (4 bytes).
    Removing distancefunctions.o(.revsh_text), (4 bytes).
    Removing distancefunctions.o(.rrx_text), (6 bytes).
    Removing distancefunctions.o(i.arm_boolean_distance_TF_FT), (172 bytes).
    Removing distancefunctions.o(i.arm_boolean_distance_TT), (114 bytes).
    Removing distancefunctions.o(i.arm_boolean_distance_TT_FF_TF_FT), (276 bytes).
    Removing distancefunctions.o(i.arm_boolean_distance_TT_TF_FT), (224 bytes).
    Removing distancefunctions.o(i.arm_braycurtis_distance_f32), (60 bytes).
    Removing distancefunctions.o(i.arm_canberra_distance_f32), (76 bytes).
    Removing distancefunctions.o(i.arm_chebyshev_distance_f32), (56 bytes).
    Removing distancefunctions.o(i.arm_chebyshev_distance_f64), (108 bytes).
    Removing distancefunctions.o(i.arm_cityblock_distance_f32), (40 bytes).
    Removing distancefunctions.o(i.arm_cityblock_distance_f64), (92 bytes).
    Removing distancefunctions.o(i.arm_correlation_distance_f32), (200 bytes).
    Removing distancefunctions.o(i.arm_cosine_distance_f32), (88 bytes).
    Removing distancefunctions.o(i.arm_cosine_distance_f64), (112 bytes).
    Removing distancefunctions.o(i.arm_dice_distance), (132 bytes).
    Removing distancefunctions.o(i.arm_dtw_distance_f32), (464 bytes).
    Removing distancefunctions.o(i.arm_dtw_init_window_q7), (172 bytes).
    Removing distancefunctions.o(i.arm_dtw_path_f32), (288 bytes).
    Removing distancefunctions.o(i.arm_euclidean_distance_f32), (56 bytes).
    Removing distancefunctions.o(i.arm_euclidean_distance_f64), (104 bytes).
    Removing distancefunctions.o(i.arm_hamming_distance), (74 bytes).
    Removing distancefunctions.o(i.arm_jaccard_distance), (78 bytes).
    Removing distancefunctions.o(i.arm_jensenshannon_distance_f32), (136 bytes).
    Removing distancefunctions.o(i.arm_kulsinski_distance), (86 bytes).
    Removing distancefunctions.o(i.arm_minkowski_distance_f32), (108 bytes).
    Removing distancefunctions.o(i.arm_rogerstanimoto_distance), (88 bytes).
    Removing distancefunctions.o(i.arm_russellrao_distance), (42 bytes).
    Removing distancefunctions.o(i.arm_sokalmichener_distance), (96 bytes).
    Removing distancefunctions.o(i.arm_sokalsneath_distance), (84 bytes).
    Removing distancefunctions.o(i.arm_yule_distance), (140 bytes).
    Removing distancefunctions.o(i.rel_entr), (28 bytes).
    Removing distancefunctionsf16.o(.rev16_text), (4 bytes).
    Removing distancefunctionsf16.o(.revsh_text), (4 bytes).
    Removing distancefunctionsf16.o(.rrx_text), (6 bytes).
    Removing fastmathfunctions.o(.rev16_text), (4 bytes).
    Removing fastmathfunctions.o(.revsh_text), (4 bytes).
    Removing fastmathfunctions.o(.rrx_text), (6 bytes).
    Removing fastmathfunctions.o(i.arm_atan2_f32), (540 bytes).
    Removing fastmathfunctions.o(i.arm_atan2_q15), (644 bytes).
    Removing fastmathfunctions.o(i.arm_atan2_q31), (948 bytes).
    Removing fastmathfunctions.o(i.arm_cos_f32), (132 bytes).
    Removing fastmathfunctions.o(i.arm_cos_q15), (68 bytes).
    Removing fastmathfunctions.o(i.arm_cos_q31), (64 bytes).
    Removing fastmathfunctions.o(i.arm_divide_q15), (106 bytes).
    Removing fastmathfunctions.o(i.arm_divide_q31), (118 bytes).
    Removing fastmathfunctions.o(i.arm_sin_f32), (128 bytes).
    Removing fastmathfunctions.o(i.arm_sin_q15), (60 bytes).
    Removing fastmathfunctions.o(i.arm_sin_q31), (64 bytes).
    Removing fastmathfunctions.o(i.arm_sqrt_q15), (168 bytes).
    Removing fastmathfunctions.o(i.arm_sqrt_q31), (192 bytes).
    Removing fastmathfunctions.o(i.arm_vexp_f32), (32 bytes).
    Removing fastmathfunctions.o(i.arm_vexp_f64), (32 bytes).
    Removing fastmathfunctions.o(i.arm_vlog_f32), (32 bytes).
    Removing fastmathfunctions.o(i.arm_vlog_f64), (32 bytes).
    Removing fastmathfunctions.o(i.arm_vlog_q15), (96 bytes).
    Removing fastmathfunctions.o(i.arm_vlog_q31), (92 bytes).
    Removing fastmathfunctions.o(.constdata), (112 bytes).
    Removing fastmathfunctionsf16.o(.rev16_text), (4 bytes).
    Removing fastmathfunctionsf16.o(.revsh_text), (4 bytes).
    Removing fastmathfunctionsf16.o(.rrx_text), (6 bytes).
    Removing filteringfunctions.o(.rev16_text), (4 bytes).
    Removing filteringfunctions.o(.revsh_text), (4 bytes).
    Removing filteringfunctions.o(.rrx_text), (6 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cas_df1_32x64_init_q31), (26 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cas_df1_32x64_q31), (328 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_f32), (132 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_fast_q15), (182 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_fast_q31), (148 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_init_f32), (22 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_init_q15), (26 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_init_q31), (26 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_q15), (322 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_q31), (176 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df2T_f32), (100 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df2T_f64), (224 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df2T_init_f32), (22 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df2T_init_f64), (22 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_stereo_df2T_f32), (152 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_stereo_df2T_init_f32), (22 bytes).
    Removing filteringfunctions.o(i.arm_conv_f32), (308 bytes).
    Removing filteringfunctions.o(i.arm_conv_fast_opt_q15), (192 bytes).
    Removing filteringfunctions.o(i.arm_conv_fast_q15), (1070 bytes).
    Removing filteringfunctions.o(i.arm_conv_fast_q31), (818 bytes).
    Removing filteringfunctions.o(i.arm_conv_opt_q15), (194 bytes).
    Removing filteringfunctions.o(i.arm_conv_opt_q7), (626 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_f32), (440 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_fast_opt_q15), (232 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_fast_q15), (1282 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_fast_q31), (424 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_opt_q15), (244 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_opt_q7), (660 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_q15), (1538 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_q31), (494 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_q7), (432 bytes).
    Removing filteringfunctions.o(i.arm_conv_q15), (1378 bytes).
    Removing filteringfunctions.o(i.arm_conv_q31), (350 bytes).
    Removing filteringfunctions.o(i.arm_conv_q7), (298 bytes).
    Removing filteringfunctions.o(i.arm_correlate_f32), (340 bytes).
    Removing filteringfunctions.o(i.arm_correlate_f64), (512 bytes).
    Removing filteringfunctions.o(i.arm_correlate_fast_opt_q15), (200 bytes).
    Removing filteringfunctions.o(i.arm_correlate_fast_q15), (994 bytes).
    Removing filteringfunctions.o(i.arm_correlate_fast_q31), (856 bytes).
    Removing filteringfunctions.o(i.arm_correlate_opt_q15), (210 bytes).
    Removing filteringfunctions.o(i.arm_correlate_opt_q7), (640 bytes).
    Removing filteringfunctions.o(i.arm_correlate_q15), (1270 bytes).
    Removing filteringfunctions.o(i.arm_correlate_q31), (398 bytes).
    Removing filteringfunctions.o(i.arm_correlate_q7), (324 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_f32), (120 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_f64), (180 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_fast_q15), (328 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_fast_q31), (114 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_init_f32), (50 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_init_f64), (50 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_init_q15), (50 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_init_q31), (50 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_q15), (372 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_q31), (130 bytes).
    Removing filteringfunctions.o(i.arm_fir_f32), (104 bytes).
    Removing filteringfunctions.o(i.arm_fir_f64), (176 bytes).
    Removing filteringfunctions.o(i.arm_fir_fast_q15), (112 bytes).
    Removing filteringfunctions.o(i.arm_fir_fast_q31), (94 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_f32), (28 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_f64), (28 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_q15), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_q31), (28 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_q7), (26 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_f32), (140 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_init_f32), (52 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_init_q15), (52 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_init_q31), (52 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_q15), (184 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_q31), (176 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_f32), (102 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_init_f32), (22 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_init_q15), (22 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_init_q31), (22 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_q15), (132 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_q31), (106 bytes).
    Removing filteringfunctions.o(i.arm_fir_q15), (132 bytes).
    Removing filteringfunctions.o(i.arm_fir_q31), (116 bytes).
    Removing filteringfunctions.o(i.arm_fir_q7), (100 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_f32), (444 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_init_f32), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_init_q15), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_init_q31), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_init_q7), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_q15), (512 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_q31), (492 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_q7), (498 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_f32), (132 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_init_f32), (28 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_init_q15), (28 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_init_q31), (28 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_q15), (234 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_q31), (236 bytes).
    Removing filteringfunctions.o(i.arm_levinson_durbin_f32), (240 bytes).
    Removing filteringfunctions.o(i.arm_levinson_durbin_q31), (396 bytes).
    Removing filteringfunctions.o(i.arm_lms_f32), (164 bytes).
    Removing filteringfunctions.o(i.arm_lms_init_f32), (44 bytes).
    Removing filteringfunctions.o(i.arm_lms_init_q15), (40 bytes).
    Removing filteringfunctions.o(i.arm_lms_init_q31), (40 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_f32), (208 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_init_f32), (60 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_init_q15), (56 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_init_q31), (56 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_q15), (362 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_q31), (466 bytes).
    Removing filteringfunctions.o(i.arm_lms_q15), (222 bytes).
    Removing filteringfunctions.o(i.arm_lms_q31), (244 bytes).
    Removing filteringfunctionsf16.o(.rev16_text), (4 bytes).
    Removing filteringfunctionsf16.o(.revsh_text), (4 bytes).
    Removing filteringfunctionsf16.o(.rrx_text), (6 bytes).
    Removing interpolationfunctions.o(.rev16_text), (4 bytes).
    Removing interpolationfunctions.o(.revsh_text), (4 bytes).
    Removing interpolationfunctions.o(.rrx_text), (6 bytes).
    Removing interpolationfunctions.o(i.arm_bilinear_interp_f32), (148 bytes).
    Removing interpolationfunctions.o(i.arm_bilinear_interp_q15), (156 bytes).
    Removing interpolationfunctions.o(i.arm_bilinear_interp_q31), (124 bytes).
    Removing interpolationfunctions.o(i.arm_bilinear_interp_q7), (120 bytes).
    Removing interpolationfunctions.o(i.arm_linear_interp_f32), (128 bytes).
    Removing interpolationfunctions.o(i.arm_linear_interp_q15), (72 bytes).
    Removing interpolationfunctions.o(i.arm_linear_interp_q31), (62 bytes).
    Removing interpolationfunctions.o(i.arm_linear_interp_q7), (60 bytes).
    Removing interpolationfunctions.o(i.arm_spline_f32), (244 bytes).
    Removing interpolationfunctions.o(i.arm_spline_init_f32), (416 bytes).
    Removing interpolationfunctionsf16.o(.rev16_text), (4 bytes).
    Removing interpolationfunctionsf16.o(.revsh_text), (4 bytes).
    Removing interpolationfunctionsf16.o(.rrx_text), (6 bytes).
    Removing matrixfunctions.o(.rev16_text), (4 bytes).
    Removing matrixfunctions.o(.revsh_text), (4 bytes).
    Removing matrixfunctions.o(.rrx_text), (6 bytes).
    Removing matrixfunctions.o(i.arm_householder_f32), (184 bytes).
    Removing matrixfunctions.o(i.arm_householder_f64), (284 bytes).
    Removing matrixfunctions.o(i.arm_mat_add_f32), (42 bytes).
    Removing matrixfunctions.o(i.arm_mat_add_q15), (42 bytes).
    Removing matrixfunctions.o(i.arm_mat_add_q31), (36 bytes).
    Removing matrixfunctions.o(i.arm_mat_cholesky_f32), (192 bytes).
    Removing matrixfunctions.o(i.arm_mat_cholesky_f64), (272 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_mult_f32), (152 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_mult_q15), (366 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_mult_q31), (246 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_trans_f32), (68 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_trans_q15), (66 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_trans_q31), (66 bytes).
    Removing matrixfunctions.o(i.arm_mat_init_f32), (8 bytes).
    Removing matrixfunctions.o(i.arm_mat_init_f64), (8 bytes).
    Removing matrixfunctions.o(i.arm_mat_init_q15), (8 bytes).
    Removing matrixfunctions.o(i.arm_mat_init_q31), (8 bytes).
    Removing matrixfunctions.o(i.arm_mat_inverse_f32), (696 bytes).
    Removing matrixfunctions.o(i.arm_mat_inverse_f64), (956 bytes).
    Removing matrixfunctions.o(i.arm_mat_ldlt_f32), (500 bytes).
    Removing matrixfunctions.o(i.arm_mat_ldlt_f64), (624 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_f32), (116 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_f64), (216 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_fast_q15), (782 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_fast_q31), (420 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_opt_q31), (134 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_q15), (214 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_q31), (134 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_q7), (128 bytes).
    Removing matrixfunctions.o(i.arm_mat_qr_f32), (1132 bytes).
    Removing matrixfunctions.o(i.arm_mat_qr_f64), (1568 bytes).
    Removing matrixfunctions.o(i.arm_mat_scale_f32), (34 bytes).
    Removing matrixfunctions.o(i.arm_mat_scale_q15), (48 bytes).
    Removing matrixfunctions.o(i.arm_mat_scale_q31), (54 bytes).
    Removing matrixfunctions.o(i.arm_mat_solve_lower_triangular_f32), (144 bytes).
    Removing matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64), (216 bytes).
    Removing matrixfunctions.o(i.arm_mat_solve_upper_triangular_f32), (142 bytes).
    Removing matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64), (212 bytes).
    Removing matrixfunctions.o(i.arm_mat_sub_f32), (42 bytes).
    Removing matrixfunctions.o(i.arm_mat_sub_f64), (70 bytes).
    Removing matrixfunctions.o(i.arm_mat_sub_q15), (42 bytes).
    Removing matrixfunctions.o(i.arm_mat_sub_q31), (36 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_f32), (52 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_f64), (78 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_q15), (52 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_q31), (52 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_q7), (52 bytes).
    Removing matrixfunctions.o(i.arm_mat_vec_mult_f32), (252 bytes).
    Removing matrixfunctions.o(i.arm_mat_vec_mult_q15), (560 bytes).
    Removing matrixfunctions.o(i.arm_mat_vec_mult_q31), (336 bytes).
    Removing matrixfunctions.o(i.arm_mat_vec_mult_q7), (574 bytes).
    Removing matrixfunctionsf16.o(.rev16_text), (4 bytes).
    Removing matrixfunctionsf16.o(.revsh_text), (4 bytes).
    Removing matrixfunctionsf16.o(.rrx_text), (6 bytes).
    Removing quaternionmathfunctions.o(.rev16_text), (4 bytes).
    Removing quaternionmathfunctions.o(.revsh_text), (4 bytes).
    Removing quaternionmathfunctions.o(.rrx_text), (6 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion2rotation_f32), (204 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_conjugate_f32), (68 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_inverse_f32), (112 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_norm_f32), (74 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_normalize_f32), (118 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_product_f32), (36 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_product_single_f32), (210 bytes).
    Removing quaternionmathfunctions.o(i.arm_rotation2quaternion_f32), (464 bytes).
    Removing svmfunctions.o(.rev16_text), (4 bytes).
    Removing svmfunctions.o(.revsh_text), (4 bytes).
    Removing svmfunctions.o(.rrx_text), (6 bytes).
    Removing svmfunctions.o(i.arm_svm_linear_init_f32), (22 bytes).
    Removing svmfunctions.o(i.arm_svm_linear_predict_f32), (104 bytes).
    Removing svmfunctions.o(i.arm_svm_polynomial_init_f32), (28 bytes).
    Removing svmfunctions.o(i.arm_svm_polynomial_predict_f32), (136 bytes).
    Removing svmfunctions.o(i.arm_svm_rbf_init_f32), (28 bytes).
    Removing svmfunctions.o(i.arm_svm_rbf_predict_f32), (140 bytes).
    Removing svmfunctions.o(i.arm_svm_sigmoid_init_f32), (24 bytes).
    Removing svmfunctions.o(i.arm_svm_sigmoid_predict_f32), (140 bytes).
    Removing svmfunctionsf16.o(.rev16_text), (4 bytes).
    Removing svmfunctionsf16.o(.revsh_text), (4 bytes).
    Removing svmfunctionsf16.o(.rrx_text), (6 bytes).
    Removing statisticsfunctions.o(.rev16_text), (4 bytes).
    Removing statisticsfunctions.o(.revsh_text), (4 bytes).
    Removing statisticsfunctions.o(.rrx_text), (6 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_f32), (54 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_f64), (102 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_f64), (84 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_q15), (146 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_q31), (122 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_q7), (146 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_q15), (182 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_q31), (156 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_q7), (182 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_f32), (54 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_f64), (102 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_f64), (84 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_q15), (146 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_q31), (122 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_q7), (146 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_q15), (182 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_q31), (156 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_q7), (182 bytes).
    Removing statisticsfunctions.o(i.arm_accumulate_f32), (32 bytes).
    Removing statisticsfunctions.o(i.arm_accumulate_f64), (60 bytes).
    Removing statisticsfunctions.o(i.arm_entropy_f32), (52 bytes).
    Removing statisticsfunctions.o(i.arm_entropy_f64), (88 bytes).
    Removing statisticsfunctions.o(i.arm_kullback_leibler_f32), (60 bytes).
    Removing statisticsfunctions.o(i.arm_kullback_leibler_f64), (104 bytes).
    Removing statisticsfunctions.o(i.arm_logsumexp_dot_prod_f32), (26 bytes).
    Removing statisticsfunctions.o(i.arm_logsumexp_f32), (96 bytes).
    Removing statisticsfunctions.o(i.arm_max_f32), (46 bytes).
    Removing statisticsfunctions.o(i.arm_max_f64), (78 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_f64), (72 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_q15), (26 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_q31), (22 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_q7), (26 bytes).
    Removing statisticsfunctions.o(i.arm_max_q15), (36 bytes).
    Removing statisticsfunctions.o(i.arm_max_q31), (32 bytes).
    Removing statisticsfunctions.o(i.arm_max_q7), (36 bytes).
    Removing statisticsfunctions.o(i.arm_mean_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_mean_f64), (96 bytes).
    Removing statisticsfunctions.o(i.arm_mean_q15), (28 bytes).
    Removing statisticsfunctions.o(i.arm_mean_q31), (44 bytes).
    Removing statisticsfunctions.o(i.arm_mean_q7), (28 bytes).
    Removing statisticsfunctions.o(i.arm_min_f32), (46 bytes).
    Removing statisticsfunctions.o(i.arm_min_f64), (78 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_f64), (72 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_q15), (26 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_q31), (22 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_q7), (26 bytes).
    Removing statisticsfunctions.o(i.arm_min_q15), (36 bytes).
    Removing statisticsfunctions.o(i.arm_min_q31), (32 bytes).
    Removing statisticsfunctions.o(i.arm_min_q7), (36 bytes).
    Removing statisticsfunctions.o(i.arm_mse_f32), (56 bytes).
    Removing statisticsfunctions.o(i.arm_mse_f64), (116 bytes).
    Removing statisticsfunctions.o(i.arm_mse_q15), (66 bytes).
    Removing statisticsfunctions.o(i.arm_mse_q31), (78 bytes).
    Removing statisticsfunctions.o(i.arm_mse_q7), (52 bytes).
    Removing statisticsfunctions.o(i.arm_power_f32), (32 bytes).
    Removing statisticsfunctions.o(i.arm_power_f64), (72 bytes).
    Removing statisticsfunctions.o(i.arm_power_q15), (28 bytes).
    Removing statisticsfunctions.o(i.arm_power_q31), (40 bytes).
    Removing statisticsfunctions.o(i.arm_power_q7), (26 bytes).
    Removing statisticsfunctions.o(i.arm_rms_f32), (68 bytes).
    Removing statisticsfunctions.o(i.arm_rms_q15), (58 bytes).
    Removing statisticsfunctions.o(i.arm_rms_q31), (68 bytes).
    Removing statisticsfunctions.o(i.arm_std_f32), (48 bytes).
    Removing statisticsfunctions.o(i.arm_std_f64), (24 bytes).
    Removing statisticsfunctions.o(i.arm_std_q15), (92 bytes).
    Removing statisticsfunctions.o(i.arm_std_q31), (124 bytes).
    Removing statisticsfunctions.o(i.arm_var_f32), (108 bytes).
    Removing statisticsfunctions.o(i.arm_var_f64), (152 bytes).
    Removing statisticsfunctions.o(i.arm_var_q15), (82 bytes).
    Removing statisticsfunctions.o(i.arm_var_q31), (120 bytes).
    Removing statisticsfunctionsf16.o(.rev16_text), (4 bytes).
    Removing statisticsfunctionsf16.o(.revsh_text), (4 bytes).
    Removing statisticsfunctionsf16.o(.rrx_text), (6 bytes).
    Removing supportfunctions.o(.rev16_text), (4 bytes).
    Removing supportfunctions.o(.revsh_text), (4 bytes).
    Removing supportfunctions.o(.rrx_text), (6 bytes).
    Removing supportfunctions.o(i.arm_barycenter_f32), (104 bytes).
    Removing supportfunctions.o(i.arm_bitonic_sort_core_f32), (136 bytes).
    Removing supportfunctions.o(i.arm_bitonic_sort_f32), (80 bytes).
    Removing supportfunctions.o(i.arm_bubble_sort_f32), (90 bytes).
    Removing supportfunctions.o(i.arm_copy_f32), (20 bytes).
    Removing supportfunctions.o(i.arm_copy_f64), (20 bytes).
    Removing supportfunctions.o(i.arm_copy_q15), (20 bytes).
    Removing supportfunctions.o(i.arm_copy_q31), (14 bytes).
    Removing supportfunctions.o(i.arm_copy_q7), (20 bytes).
    Removing supportfunctions.o(i.arm_f64_to_float), (34 bytes).
    Removing supportfunctions.o(i.arm_f64_to_q15), (72 bytes).
    Removing supportfunctions.o(i.arm_f64_to_q31), (72 bytes).
    Removing supportfunctions.o(i.arm_f64_to_q7), (72 bytes).
    Removing supportfunctions.o(i.arm_fill_f32), (16 bytes).
    Removing supportfunctions.o(i.arm_fill_f64), (16 bytes).
    Removing supportfunctions.o(i.arm_fill_q15), (16 bytes).
    Removing supportfunctions.o(i.arm_fill_q31), (12 bytes).
    Removing supportfunctions.o(i.arm_fill_q7), (16 bytes).
    Removing supportfunctions.o(i.arm_float_to_f64), (28 bytes).
    Removing supportfunctions.o(i.arm_float_to_q15), (44 bytes).
    Removing supportfunctions.o(i.arm_float_to_q31), (60 bytes).
    Removing supportfunctions.o(i.arm_float_to_q7), (44 bytes).
    Removing supportfunctions.o(i.arm_heap_sort_f32), (94 bytes).
    Removing supportfunctions.o(i.arm_heapify), (136 bytes).
    Removing supportfunctions.o(i.arm_insertion_sort_f32), (88 bytes).
    Removing supportfunctions.o(i.arm_merge_sort_core_f32), (156 bytes).
    Removing supportfunctions.o(i.arm_merge_sort_f32), (60 bytes).
    Removing supportfunctions.o(i.arm_merge_sort_init_f32), (6 bytes).
    Removing supportfunctions.o(i.arm_q15_to_f64), (60 bytes).
    Removing supportfunctions.o(i.arm_q15_to_float), (40 bytes).
    Removing supportfunctions.o(i.arm_q15_to_q31), (20 bytes).
    Removing supportfunctions.o(i.arm_q15_to_q7), (22 bytes).
    Removing supportfunctions.o(i.arm_q31_to_f64), (56 bytes).
    Removing supportfunctions.o(i.arm_q31_to_float), (36 bytes).
    Removing supportfunctions.o(i.arm_q31_to_q15), (18 bytes).
    Removing supportfunctions.o(i.arm_q31_to_q7), (18 bytes).
    Removing supportfunctions.o(i.arm_q7_to_f64), (60 bytes).
    Removing supportfunctions.o(i.arm_q7_to_float), (40 bytes).
    Removing supportfunctions.o(i.arm_q7_to_q15), (22 bytes).
    Removing supportfunctions.o(i.arm_q7_to_q31), (20 bytes).
    Removing supportfunctions.o(i.arm_quick_sort_core_f32), (178 bytes).
    Removing supportfunctions.o(i.arm_quick_sort_f32), (40 bytes).
    Removing supportfunctions.o(i.arm_selection_sort_f32), (122 bytes).
    Removing supportfunctions.o(i.arm_sort_f32), (58 bytes).
    Removing supportfunctions.o(i.arm_sort_init_f32), (6 bytes).
    Removing supportfunctions.o(i.arm_weighted_average_f32), (44 bytes).
    Removing supportfunctionsf16.o(.rev16_text), (4 bytes).
    Removing supportfunctionsf16.o(.revsh_text), (4 bytes).
    Removing supportfunctionsf16.o(.rrx_text), (6 bytes).
    Removing transformfunctions.o(.rev16_text), (4 bytes).
    Removing transformfunctions.o(.revsh_text), (4 bytes).
    Removing transformfunctions.o(.rrx_text), (6 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_16), (62 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_32), (62 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_64), (94 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_q15), (102 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_q31), (170 bytes).
    Removing transformfunctions.o(i.arm_cfft_f32), (204 bytes).
    Removing transformfunctions.o(i.arm_cfft_f64), (256 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_1024_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_1024_f64), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_1024_q15), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_1024_q31), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_128_f32), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_128_f64), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_128_q15), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_128_q31), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_16_f32), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_16_f64), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_16_q15), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_16_q31), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_2048_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_2048_f64), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_2048_q15), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_2048_q31), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_256_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_256_f64), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_256_q15), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_256_q31), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_32_f32), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_32_f64), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_32_q15), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_32_q31), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_4096_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_4096_f64), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_4096_q15), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_4096_q31), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_512_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_512_f64), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_512_q15), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_512_q31), (32 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_64_f32), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_64_f64), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_64_q15), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_64_q31), (28 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_f32), (92 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_f64), (92 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_q15), (92 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_q31), (92 bytes).
    Removing transformfunctions.o(i.arm_cfft_q15), (172 bytes).
    Removing transformfunctions.o(i.arm_cfft_q31), (172 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_f32), (60 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_init_f32), (284 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_init_q15), (192 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_init_q31), (192 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_q15), (46 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_q31), (46 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_init_q15), (124 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_init_q31), (124 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_q15), (54 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_q31), (54 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_f64), (286 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_inverse_q15), (190 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_inverse_q31), (190 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_q15), (190 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_q31), (190 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix8by2_f32), (392 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix8by4_f32), (1068 bytes).
    Removing transformfunctions.o(i.arm_dct4_f32), (196 bytes).
    Removing transformfunctions.o(i.arm_dct4_init_f32), (132 bytes).
    Removing transformfunctions.o(i.arm_dct4_init_q15), (128 bytes).
    Removing transformfunctions.o(i.arm_dct4_init_q31), (128 bytes).
    Removing transformfunctions.o(i.arm_dct4_q15), (180 bytes).
    Removing transformfunctions.o(i.arm_dct4_q31), (170 bytes).
    Removing transformfunctions.o(i.arm_mfcc_f32), (276 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_1024_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_1024_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_1024_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_128_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_128_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_128_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_2048_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_2048_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_2048_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_256_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_256_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_256_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_32_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_32_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_32_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_4096_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_4096_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_4096_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_512_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_512_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_512_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_64_f32), (32 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_64_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_64_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_f32), (34 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_q15), (352 bytes).
    Removing transformfunctions.o(i.arm_mfcc_q31), (320 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_f32), (334 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_inverse_f32), (342 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_inverse_q15), (614 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_inverse_q31), (440 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_q15), (658 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_q31), (432 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_f64), (768 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_inverse_q15), (908 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_inverse_q31), (944 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_q15), (908 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_q31), (944 bytes).
    Removing transformfunctions.o(i.arm_radix8_butterfly_f32), (1220 bytes).
    Removing transformfunctions.o(i.arm_rfft_f32), (126 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_f32), (56 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_f64), (68 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_1024_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_1024_f64), (52 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_128_f32), (36 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_128_f64), (48 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_2048_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_2048_f64), (52 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_256_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_256_f64), (48 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_32_f32), (36 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_32_f64), (48 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_4096_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_4096_f64), (52 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_512_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_512_f64), (52 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_64_f32), (36 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_64_f64), (48 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_f32), (84 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_f64), (84 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_1024_q15), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_1024_q31), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_128_q15), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_128_q31), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_2048_q15), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_2048_q31), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_256_q15), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_256_q31), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_32_q15), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_32_q31), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_4096_q15), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_4096_q31), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_512_q15), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_512_q31), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_64_q15), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_64_q31), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_8192_q15), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_8192_q31), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_f32), (116 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_q15), (172 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_q31), (172 bytes).
    Removing transformfunctions.o(i.arm_rfft_q15), (100 bytes).
    Removing transformfunctions.o(i.arm_rfft_q31), (100 bytes).
    Removing transformfunctions.o(i.arm_split_rfft_f32), (200 bytes).
    Removing transformfunctions.o(i.arm_split_rfft_q15), (276 bytes).
    Removing transformfunctions.o(i.arm_split_rfft_q31), (226 bytes).
    Removing transformfunctions.o(i.arm_split_rifft_f32), (110 bytes).
    Removing transformfunctions.o(i.arm_split_rifft_q15), (166 bytes).
    Removing transformfunctions.o(i.arm_split_rifft_q31), (100 bytes).
    Removing transformfunctions.o(i.merge_rfft_f32), (160 bytes).
    Removing transformfunctions.o(i.merge_rfft_f64), (332 bytes).
    Removing transformfunctions.o(i.stage_rfft_f32), (164 bytes).
    Removing transformfunctions.o(i.stage_rfft_f64), (360 bytes).
    Removing transformfunctionsf16.o(.rev16_text), (4 bytes).
    Removing transformfunctionsf16.o(.revsh_text), (4 bytes).
    Removing transformfunctionsf16.o(.rrx_text), (6 bytes).
    Removing windowfunctions.o(.rev16_text), (4 bytes).
    Removing windowfunctions.o(.revsh_text), (4 bytes).
    Removing windowfunctions.o(.rrx_text), (6 bytes).
    Removing windowfunctions.o(i.arm_bartlett_f32), (80 bytes).
    Removing windowfunctions.o(i.arm_bartlett_f64), (160 bytes).
    Removing windowfunctions.o(i.arm_blackman_harris_92db_f32), (152 bytes).
    Removing windowfunctions.o(i.arm_blackman_harris_92db_f64), (288 bytes).
    Removing windowfunctions.o(i.arm_hamming_f32), (104 bytes).
    Removing windowfunctions.o(i.arm_hamming_f64), (160 bytes).
    Removing windowfunctions.o(i.arm_hanning_f32), (96 bytes).
    Removing windowfunctions.o(i.arm_hanning_f64), (160 bytes).
    Removing windowfunctions.o(i.arm_hft116d_f32), (192 bytes).
    Removing windowfunctions.o(i.arm_hft116d_f64), (424 bytes).
    Removing windowfunctions.o(i.arm_hft144d_f32), (216 bytes).
    Removing windowfunctions.o(i.arm_hft144d_f64), (492 bytes).
    Removing windowfunctions.o(i.arm_hft169d_f32), (240 bytes).
    Removing windowfunctions.o(i.arm_hft169d_f64), (564 bytes).
    Removing windowfunctions.o(i.arm_hft196d_f32), (264 bytes).
    Removing windowfunctions.o(i.arm_hft196d_f64), (644 bytes).
    Removing windowfunctions.o(i.arm_hft223d_f32), (288 bytes).
    Removing windowfunctions.o(i.arm_hft223d_f64), (720 bytes).
    Removing windowfunctions.o(i.arm_hft248d_f32), (312 bytes).
    Removing windowfunctions.o(i.arm_hft248d_f64), (796 bytes).
    Removing windowfunctions.o(i.arm_hft90d_f32), (168 bytes).
    Removing windowfunctions.o(i.arm_hft90d_f64), (356 bytes).
    Removing windowfunctions.o(i.arm_hft95_f32), (168 bytes).
    Removing windowfunctions.o(i.arm_hft95_f64), (356 bytes).
    Removing windowfunctions.o(i.arm_nuttall3_f32), (116 bytes).
    Removing windowfunctions.o(i.arm_nuttall3_f64), (220 bytes).
    Removing windowfunctions.o(i.arm_nuttall3a_f32), (124 bytes).
    Removing windowfunctions.o(i.arm_nuttall3a_f64), (220 bytes).
    Removing windowfunctions.o(i.arm_nuttall3b_f32), (128 bytes).
    Removing windowfunctions.o(i.arm_nuttall3b_f64), (220 bytes).
    Removing windowfunctions.o(i.arm_nuttall4_f32), (140 bytes).
    Removing windowfunctions.o(i.arm_nuttall4_f64), (288 bytes).
    Removing windowfunctions.o(i.arm_nuttall4a_f32), (152 bytes).
    Removing windowfunctions.o(i.arm_nuttall4a_f64), (288 bytes).
    Removing windowfunctions.o(i.arm_nuttall4b_f32), (152 bytes).
    Removing windowfunctions.o(i.arm_nuttall4b_f64), (288 bytes).
    Removing windowfunctions.o(i.arm_nuttall4c_f32), (152 bytes).
    Removing windowfunctions.o(i.arm_nuttall4c_f64), (288 bytes).
    Removing windowfunctions.o(i.arm_welch_f32), (68 bytes).
    Removing windowfunctions.o(i.arm_welch_f64), (128 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing ffixl.o(.text), (66 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing dfixl.o(.text), (74 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing fepilogue.o(.text), (110 bytes).
    Removing dsqrt.o(.text), (162 bytes).
    Removing fpstat.o(.text), (4 bytes).

1470 unused section(s) (total 1031471 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixl.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixl.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../fplib/microlib/fpstat.c               0x00000000   Number         0  fpstat.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/exp.c                         0x00000000   Number         0  exp_x.o ABSOLUTE
    ../mathlib/exp.c                         0x00000000   Number         0  exp.o ABSOLUTE
    ../mathlib/expf.c                        0x00000000   Number         0  expf_x.o ABSOLUTE
    ../mathlib/expf.c                        0x00000000   Number         0  expf.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log_x.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log.o ABSOLUTE
    ../mathlib/logf.c                        0x00000000   Number         0  logf_x.o ABSOLUTE
    ../mathlib/logf.c                        0x00000000   Number         0  logf.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf_x.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/tanhf.c                       0x00000000   Number         0  tanhf_x.o ABSOLUTE
    ../mathlib/tanhf.c                       0x00000000   Number         0  tanhf.o ABSOLUTE
    ..\APP\adc_app.c                         0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\APP\led_app.c                         0x00000000   Number         0  led_app.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\uart_app.c                        0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\APP\waveform_analyzer_app.c           0x00000000   Number         0  waveform_analyzer_app.o ABSOLUTE
    ..\Components\LCD\lcd.c                  0x00000000   Number         0  lcd.o ABSOLUTE
    ..\Components\TLC5615\tlc5615.c          0x00000000   Number         0  tlc5615.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\\APP\\adc_app.c                       0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\APP\\led_app.c                       0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\uart_app.c                      0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\APP\\waveform_analyzer_app.c         0x00000000   Number         0  waveform_analyzer_app.o ABSOLUTE
    ..\\Components\\LCD\\lcd.c               0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\Components\\TLC5615\\tlc5615.c       0x00000000   Number         0  tlc5615.o ABSOLUTE
    ..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/BasicMathFunctions/BasicMathFunctions.c 0x00000000   Number         0  basicmathfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/BasicMathFunctions/BasicMathFunctionsF16.c 0x00000000   Number         0  basicmathfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/BayesFunctions/BayesFunctions.c 0x00000000   Number         0  bayesfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/BayesFunctions/BayesFunctionsF16.c 0x00000000   Number         0  bayesfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/CommonTables/CommonTables.c 0x00000000   Number         0  commontables.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/CommonTables/CommonTablesF16.c 0x00000000   Number         0  commontablesf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/ComplexMathFunctions/ComplexMathFunctions.c 0x00000000   Number         0  complexmathfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/ComplexMathFunctions/ComplexMathFunctionsF16.c 0x00000000   Number         0  complexmathfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/ControllerFunctions/ControllerFunctions.c 0x00000000   Number         0  controllerfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/DistanceFunctions/DistanceFunctions.c 0x00000000   Number         0  distancefunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/DistanceFunctions/DistanceFunctionsF16.c 0x00000000   Number         0  distancefunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/FastMathFunctions.c 0x00000000   Number         0  fastmathfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/FastMathFunctionsF16.c 0x00000000   Number         0  fastmathfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FilteringFunctions/FilteringFunctions.c 0x00000000   Number         0  filteringfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/FilteringFunctions/FilteringFunctionsF16.c 0x00000000   Number         0  filteringfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/InterpolationFunctions/InterpolationFunctions.c 0x00000000   Number         0  interpolationfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/InterpolationFunctions/InterpolationFunctionsF16.c 0x00000000   Number         0  interpolationfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/MatrixFunctions.c 0x00000000   Number         0  matrixfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/MatrixFunctionsF16.c 0x00000000   Number         0  matrixfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/QuaternionMathFunctions.c 0x00000000   Number         0  quaternionmathfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/SVMFunctions/SVMFunctions.c 0x00000000   Number         0  svmfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/SVMFunctions/SVMFunctionsF16.c 0x00000000   Number         0  svmfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/StatisticsFunctions/StatisticsFunctions.c 0x00000000   Number         0  statisticsfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/StatisticsFunctions/StatisticsFunctionsF16.c 0x00000000   Number         0  statisticsfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/SupportFunctions/SupportFunctions.c 0x00000000   Number         0  supportfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/SupportFunctions/SupportFunctionsF16.c 0x00000000   Number         0  supportfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/TransformFunctions/TransformFunctions.c 0x00000000   Number         0  transformfunctions.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/TransformFunctions/TransformFunctionsF16.c 0x00000000   Number         0  transformfunctionsf16.o ABSOLUTE
    E:/Keil_5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/WindowFunctions/WindowFunctions.c 0x00000000   Number         0  windowfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\BasicMathFunctions.c 0x00000000   Number         0  basicmathfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\BasicMathFunctionsF16.c 0x00000000   Number         0  basicmathfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\BayesFunctions\BayesFunctions.c 0x00000000   Number         0  bayesfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\BayesFunctions\BayesFunctionsF16.c 0x00000000   Number         0  bayesfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\CommonTables\CommonTables.c 0x00000000   Number         0  commontables.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\CommonTables\CommonTablesF16.c 0x00000000   Number         0  commontablesf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\ComplexMathFunctions.c 0x00000000   Number         0  complexmathfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\ComplexMathFunctionsF16.c 0x00000000   Number         0  complexmathfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\ControllerFunctions.c 0x00000000   Number         0  controllerfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\DistanceFunctions.c 0x00000000   Number         0  distancefunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\DistanceFunctionsF16.c 0x00000000   Number         0  distancefunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\FastMathFunctions.c 0x00000000   Number         0  fastmathfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\FastMathFunctionsF16.c 0x00000000   Number         0  fastmathfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\FilteringFunctions.c 0x00000000   Number         0  filteringfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\FilteringFunctionsF16.c 0x00000000   Number         0  filteringfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\InterpolationFunctions.c 0x00000000   Number         0  interpolationfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\InterpolationFunctionsF16.c 0x00000000   Number         0  interpolationfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\MatrixFunctions.c 0x00000000   Number         0  matrixfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\MatrixFunctionsF16.c 0x00000000   Number         0  matrixfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\QuaternionMathFunctions.c 0x00000000   Number         0  quaternionmathfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\SVMFunctions.c 0x00000000   Number         0  svmfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\SVMFunctionsF16.c 0x00000000   Number         0  svmfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\StatisticsFunctions.c 0x00000000   Number         0  statisticsfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\StatisticsFunctionsF16.c 0x00000000   Number         0  statisticsfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\SupportFunctions.c 0x00000000   Number         0  supportfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\SupportFunctionsF16.c 0x00000000   Number         0  supportfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\TransformFunctions.c 0x00000000   Number         0  transformfunctions.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\TransformFunctionsF16.c 0x00000000   Number         0  transformfunctionsf16.o ABSOLUTE
    E:\Keil_5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\WindowFunctions.c 0x00000000   Number         0  windowfunctions.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x080001a0   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c4   Section        0  uldiv.o(.text)
    .text                                    0x08000226   Section        0  llshl.o(.text)
    .text                                    0x08000244   Section        0  llsshr.o(.text)
    .text                                    0x08000268   Section        0  memcpya.o(.text)
    .text                                    0x0800028c   Section        0  memseta.o(.text)
    .text                                    0x080002b0   Section        0  dadd.o(.text)
    .text                                    0x080003fe   Section        0  dmul.o(.text)
    .text                                    0x080004e2   Section        0  ddiv.o(.text)
    .text                                    0x080005c0   Section        0  f2d.o(.text)
    .text                                    0x080005e8   Section       48  cdrcmple.o(.text)
    .text                                    0x08000618   Section        0  uidiv.o(.text)
    .text                                    0x08000644   Section        0  llushr.o(.text)
    .text                                    0x08000664   Section        0  iusefp.o(.text)
    .text                                    0x08000664   Section        0  depilogue.o(.text)
    .text                                    0x0800071e   Section        0  dfixul.o(.text)
    .text                                    0x08000750   Section       36  init.o(.text)
    i.ADC_DMAConvCplt                        0x08000774   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x08000775   Thumb Code   110  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x080007e2   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x080007e3   Thumb Code    22  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x080007f8   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x080007f9   Thumb Code    10  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_IRQHandler                         0x08000804   Section        0  stm32f4xx_it.o(i.ADC_IRQHandler)
    i.ADC_Init                               0x08000810   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08000811   Thumb Code   284  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.Analyze_Frequency_And_Type             0x08000938   Section        0  waveform_analyzer_app.o(i.Analyze_Frequency_And_Type)
    i.Analyze_Harmonics                      0x08000b2c   Section        0  waveform_analyzer_app.o(i.Analyze_Harmonics)
    i.BusFault_Handler                       0x08000e40   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.Calculate_Phase_Difference             0x08000e44   Section        0  waveform_analyzer_app.o(i.Calculate_Phase_Difference)
    i.DMA2_Stream0_IRQHandler                0x08000e7c   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08000e88   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08000e94   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08000e95   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08000ebc   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08000ebd   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08000f10   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000f11   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08000f38   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_us                               0x08000f3c   Section        0  main.o(i.Delay_us)
    i.Error_Handler                          0x08000f6c   Section        0  main.o(i.Error_Handler)
    i.Get_Component_Phase                    0x08000f70   Section        0  waveform_analyzer_app.o(i.Get_Component_Phase)
    i.Get_Waveform_Info                      0x08000f80   Section        0  waveform_analyzer_app.o(i.Get_Waveform_Info)
    i.Get_Waveform_Phase                     0x08000fe0   Section        0  waveform_analyzer_app.o(i.Get_Waveform_Phase)
    i.Get_Waveform_Vpp                       0x080010a0   Section        0  waveform_analyzer_app.o(i.Get_Waveform_Vpp)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x08001138   Section        0  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADC_ConfigChannel                  0x0800113c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08001288   Section        0  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x080012a4   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x080012a6   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_IRQHandler                     0x080012a8   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x080013d6   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x0800142a   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x0800142c   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x080014d0   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_ADC_Stop_DMA                       0x08001628   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DMA_Abort                          0x08001694   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001726   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x0800174c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x080018ec   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080019c0   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_GPIO_Init                          0x08001a30   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08001c20   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001c2c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08001c38   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001c48   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001c7c   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001cbc   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001cf0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001d0c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001d4c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001d70   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08001ea4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08001eb0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001ed0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08001ef0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001f50   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x080022bc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x080022e4   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08002374   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080023d0   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x080023f8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_ConfigClockSource              0x08002470   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x0800254c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08002598   Section        0  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x080025f0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08002660   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08002664   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080028e4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002948   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x080029f8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08002a14   Section        0  uart_app.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08002a50   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08002a52   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08002af2   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002af4   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_ADC1_Init                           0x08002af8   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DMA_Init                            0x08002b6c   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08002ba8   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM3_Init                           0x08002c6c   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_USART1_UART_Init                    0x08002cd4   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.Map_Input_To_FFT_Frequency             0x08002d24   Section        0  waveform_analyzer_app.o(i.Map_Input_To_FFT_Frequency)
    i.MemManage_Handler                      0x08002e18   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.My_FFT_Init                            0x08002e1c   Section        0  waveform_analyzer_app.o(i.My_FFT_Init)
    i.NMI_Handler                            0x08002e2c   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08002e2e   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08002e30   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08002e32   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08002e38   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08002ecc   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x08002edc   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08002fac   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08002fc0   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08002fc1   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08002fd0   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08002fd1   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08002ff2   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08002ff3   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x08003016   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08003017   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08003024   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08003025   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x0800306e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x0800306f   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x080030f4   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x080030f5   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08003112   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08003113   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08003160   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08003161   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x0800317c   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800317d   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08003240   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08003241   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x0800334c   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x080033ec   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08003422   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08003423   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08003494   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x080034a0   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0vsnprintf                           0x080034a4   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassifyf                      0x080034d8   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__NVIC_SetPriority                     0x080034fe   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080034ff   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_atan2f                        0x08003520   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_sqrtf                         0x080037cc   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_infnan2                  0x08003806   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_underflow                0x0800380c   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__scatterload_copy                     0x0800381c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800382a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800382c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x0800383c   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x08003848   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08003849   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080039cc   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080039cd   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08004080   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08004081   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080040a4   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080040a5   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x080040d2   Section        0  printfa.o(i._snputc)
    _snputc                                  0x080040d3   Thumb Code    22  printfa.o(i._snputc)
    i.adc_task                               0x080040e8   Section        0  adc_app.o(i.adc_task)
    i.adc_tim_dma_init                       0x08004164   Section        0  adc_app.o(i.adc_tim_dma_init)
    i.arm_bitreversal_f32                    0x08004198   Section        0  transformfunctions.o(i.arm_bitreversal_f32)
    i.arm_cfft_radix4_f32                    0x08004240   Section        0  transformfunctions.o(i.arm_cfft_radix4_f32)
    i.arm_cfft_radix4_init_f32               0x0800427c   Section        0  transformfunctions.o(i.arm_cfft_radix4_init_f32)
    i.arm_cmplx_mag_f32                      0x0800432c   Section        0  complexmathfunctions.o(i.arm_cmplx_mag_f32)
    i.arm_radix4_butterfly_f32               0x08004370   Section        0  transformfunctions.o(i.arm_radix4_butterfly_f32)
    i.arm_radix4_butterfly_inverse_f32       0x080044b4   Section        0  transformfunctions.o(i.arm_radix4_butterfly_inverse_f32)
    i.delay_ms                               0x080046cc   Section        0  main.o(i.delay_ms)
    i.display_harmonic_results               0x080046e0   Section        0  adc_app.o(i.display_harmonic_results)
    i.lcd_draw_point                         0x080049b8   Section        0  lcd.o(i.lcd_draw_point)
    i.lcd_fill                               0x080049d0   Section        0  lcd.o(i.lcd_fill)
    i.lcd_pow                                0x08004a18   Section        0  lcd.o(i.lcd_pow)
    lcd_pow                                  0x08004a19   Thumb Code    16  lcd.o(i.lcd_pow)
    i.lcd_set_cursor                         0x08004a28   Section        0  lcd.o(i.lcd_set_cursor)
    i.lcd_show_char                          0x08004b1c   Section        0  lcd.o(i.lcd_show_char)
    i.lcd_show_float                         0x08004bf8   Section        0  lcd.o(i.lcd_show_float)
    i.lcd_show_num                           0x08004c8c   Section        0  lcd.o(i.lcd_show_num)
    i.lcd_show_string                        0x08004cfe   Section        0  lcd.o(i.lcd_show_string)
    i.lcd_show_xnum                          0x08004d54   Section        0  lcd.o(i.lcd_show_xnum)
    i.lcd_wr_data                            0x08004dde   Section        0  lcd.o(i.lcd_wr_data)
    i.lcd_wr_regno                           0x08004df6   Section        0  lcd.o(i.lcd_wr_regno)
    i.lcd_write_ram_prepare                  0x08004e10   Section        0  lcd.o(i.lcd_write_ram_prepare)
    i.main                                   0x08004e24   Section        0  main.o(i.main)
    i.my_printf                              0x08004e58   Section        0  uart_app.o(i.my_printf)
    i.print_harmonic_data                    0x08004e94   Section        0  adc_app.o(i.print_harmonic_data)
    i.scheduler_init                         0x08005050   Section        0  scheduler.o(i.scheduler_init)
    i.test_tlc5615_voltage                   0x0800505c   Section        0  tlc5615.o(i.test_tlc5615_voltage)
    i.write_5615                             0x080050b8   Section        0  tlc5615.o(i.write_5615)
    .constdata                               0x08005140   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08005140   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08005148   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08005158   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08005160   Section    12160  lcd.o(.constdata)
    .constdata                               0x080080e0   Section      160  waveform_analyzer_app.o(.constdata)
    .constdata                               0x08008180   Section     2048  commontables.o(.constdata)
    .constdata                               0x08008980   Section    32768  commontables.o(.constdata)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section        4  lcd.o(.data)
    .data                                    0x20000014   Section       16  scheduler.o(.data)
    scheduler_task                           0x20000018   Data          12  scheduler.o(.data)
    .data                                    0x20000024   Section        8  uart_app.o(.data)
    .data                                    0x2000002c   Section        1  adc_app.o(.data)
    .data                                    0x20000030   Section        4  errno.o(.data)
    _errno                                   0x20000030   Data           4  errno.o(.data)
    .bss                                     0x20000034   Section      168  adc.o(.bss)
    .bss                                     0x200000dc   Section       72  tim.o(.bss)
    .bss                                     0x20000124   Section      168  usart.o(.bss)
    .bss                                     0x200001cc   Section       94  lcd.o(.bss)
    .bss                                     0x2000022a   Section      384  uart_app.o(.bss)
    .bss                                     0x200003ac   Section    12288  adc_app.o(.bss)
    .bss                                     0x200033ac   Section    12308  waveform_analyzer_app.o(.bss)
    STACK                                    0x200063c0   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c5   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08000227   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000227   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08000245   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000245   Thumb Code     0  llsshr.o(.text)
    __aeabi_memcpy                           0x08000269   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000269   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000269   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800028d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800028d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800028d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800029b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800029b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800029b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800029f   Thumb Code    18  memseta.o(.text)
    __aeabi_dadd                             0x080002b1   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080003f3   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080003f9   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080003ff   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080004e3   Thumb Code   222  ddiv.o(.text)
    __aeabi_f2d                              0x080005c1   Thumb Code    38  f2d.o(.text)
    __aeabi_cdrcmple                         0x080005e9   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_uidiv                            0x08000619   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000619   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsr                             0x08000645   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000645   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000665   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08000665   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000683   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x0800071f   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000751   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000751   Thumb Code     0  init.o(.text)
    ADC_IRQHandler                           0x08000805   Thumb Code     6  stm32f4xx_it.o(i.ADC_IRQHandler)
    Analyze_Frequency_And_Type               0x08000939   Thumb Code   448  waveform_analyzer_app.o(i.Analyze_Frequency_And_Type)
    Analyze_Harmonics                        0x08000b2d   Thumb Code   754  waveform_analyzer_app.o(i.Analyze_Harmonics)
    BusFault_Handler                         0x08000e41   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    Calculate_Phase_Difference               0x08000e45   Thumb Code    44  waveform_analyzer_app.o(i.Calculate_Phase_Difference)
    DMA2_Stream0_IRQHandler                  0x08000e7d   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08000e89   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08000f39   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_us                                 0x08000f3d   Thumb Code    42  main.o(i.Delay_us)
    Error_Handler                            0x08000f6d   Thumb Code     4  main.o(i.Error_Handler)
    Get_Component_Phase                      0x08000f71   Thumb Code    16  waveform_analyzer_app.o(i.Get_Component_Phase)
    Get_Waveform_Info                        0x08000f81   Thumb Code    92  waveform_analyzer_app.o(i.Get_Waveform_Info)
    Get_Waveform_Phase                       0x08000fe1   Thumb Code   166  waveform_analyzer_app.o(i.Get_Waveform_Phase)
    Get_Waveform_Vpp                         0x080010a1   Thumb Code   136  waveform_analyzer_app.o(i.Get_Waveform_Vpp)
    HAL_ADCEx_InjectedConvCpltCallback       0x08001139   Thumb Code     2  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADC_ConfigChannel                    0x0800113d   Thumb Code   316  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08001289   Thumb Code    20  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x080012a5   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x080012a7   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_IRQHandler                       0x080012a9   Thumb Code   302  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x080013d7   Thumb Code    84  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x0800142b   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x0800142d   Thumb Code   144  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x080014d1   Thumb Code   306  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x08001629   Thumb Code   108  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DMA_Abort                            0x08001695   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001727   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x0800174d   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080018ed   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080019c1   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x08001a31   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08001c21   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001c2d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08001c39   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001c49   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001c7d   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001cbd   Thumb Code    48  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001cf1   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001d0d   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001d4d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001d71   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08001ea5   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08001eb1   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001ed1   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001ef1   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001f51   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080022bd   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x080022e5   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08002375   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080023d1   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x080023f9   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_ConfigClockSource                0x08002471   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_UARTEx_ReceiveToIdle_DMA             0x0800254d   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08002599   Thumb Code    66  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x080025f1   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002661   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002665   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080028e5   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002949   Thumb Code   156  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080029f9   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08002a15   Thumb Code    38  uart_app.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08002a51   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08002a53   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08002af3   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002af5   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x08002af9   Thumb Code   108  adc.o(i.MX_ADC1_Init)
    MX_DMA_Init                              0x08002b6d   Thumb Code    56  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08002ba9   Thumb Code   182  gpio.o(i.MX_GPIO_Init)
    MX_TIM3_Init                             0x08002c6d   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_USART1_UART_Init                      0x08002cd5   Thumb Code    66  usart.o(i.MX_USART1_UART_Init)
    Map_Input_To_FFT_Frequency               0x08002d25   Thumb Code   196  waveform_analyzer_app.o(i.Map_Input_To_FFT_Frequency)
    MemManage_Handler                        0x08002e19   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    My_FFT_Init                              0x08002e1d   Thumb Code    12  waveform_analyzer_app.o(i.My_FFT_Init)
    NMI_Handler                              0x08002e2d   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08002e2f   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08002e31   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08002e33   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08002e39   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x08002ecd   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x08002edd   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08002fad   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UART_Start_Receive_DMA                   0x0800334d   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x080033ed   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08003495   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x080034a1   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0vsnprintf                             0x080034a5   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x080034a5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x080034a5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x080034a5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x080034a5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassifyf                        0x080034d9   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_atan2f                          0x08003521   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_sqrtf                           0x080037cd   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_infnan2                    0x08003807   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_underflow                  0x0800380d   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __scatterload_copy                       0x0800381d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800382b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800382d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x0800383d   Thumb Code     6  errno.o(i.__set_errno)
    adc_task                                 0x080040e9   Thumb Code   102  adc_app.o(i.adc_task)
    adc_tim_dma_init                         0x08004165   Thumb Code    34  adc_app.o(i.adc_tim_dma_init)
    arm_bitreversal_f32                      0x08004199   Thumb Code   168  transformfunctions.o(i.arm_bitreversal_f32)
    arm_cfft_radix4_f32                      0x08004241   Thumb Code    60  transformfunctions.o(i.arm_cfft_radix4_f32)
    arm_cfft_radix4_init_f32                 0x0800427d   Thumb Code   144  transformfunctions.o(i.arm_cfft_radix4_init_f32)
    arm_cmplx_mag_f32                        0x0800432d   Thumb Code    62  complexmathfunctions.o(i.arm_cmplx_mag_f32)
    arm_radix4_butterfly_f32                 0x08004371   Thumb Code   324  transformfunctions.o(i.arm_radix4_butterfly_f32)
    arm_radix4_butterfly_inverse_f32         0x080044b5   Thumb Code   534  transformfunctions.o(i.arm_radix4_butterfly_inverse_f32)
    delay_ms                                 0x080046cd   Thumb Code    14  main.o(i.delay_ms)
    display_harmonic_results                 0x080046e1   Thumb Code   564  adc_app.o(i.display_harmonic_results)
    lcd_draw_point                           0x080049b9   Thumb Code    22  lcd.o(i.lcd_draw_point)
    lcd_fill                                 0x080049d1   Thumb Code    72  lcd.o(i.lcd_fill)
    lcd_set_cursor                           0x08004a29   Thumb Code   238  lcd.o(i.lcd_set_cursor)
    lcd_show_char                            0x08004b1d   Thumb Code   196  lcd.o(i.lcd_show_char)
    lcd_show_float                           0x08004bf9   Thumb Code   148  lcd.o(i.lcd_show_float)
    lcd_show_num                             0x08004c8d   Thumb Code   114  lcd.o(i.lcd_show_num)
    lcd_show_string                          0x08004cff   Thumb Code    86  lcd.o(i.lcd_show_string)
    lcd_show_xnum                            0x08004d55   Thumb Code   138  lcd.o(i.lcd_show_xnum)
    lcd_wr_data                              0x08004ddf   Thumb Code    24  lcd.o(i.lcd_wr_data)
    lcd_wr_regno                             0x08004df7   Thumb Code    24  lcd.o(i.lcd_wr_regno)
    lcd_write_ram_prepare                    0x08004e11   Thumb Code    14  lcd.o(i.lcd_write_ram_prepare)
    main                                     0x08004e25   Thumb Code    52  main.o(i.main)
    my_printf                                0x08004e59   Thumb Code    60  uart_app.o(i.my_printf)
    print_harmonic_data                      0x08004e95   Thumb Code   238  adc_app.o(i.print_harmonic_data)
    scheduler_init                           0x08005051   Thumb Code     8  scheduler.o(i.scheduler_init)
    test_tlc5615_voltage                     0x0800505d   Thumb Code    92  tlc5615.o(i.test_tlc5615_voltage)
    write_5615                               0x080050b9   Thumb Code   132  tlc5615.o(i.write_5615)
    AHBPrescTable                            0x08005148   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08005158   Data           8  system_stm32f4xx.o(.constdata)
    asc2_1206                                0x08005160   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x080055d4   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x08005bc4   Data        3420  lcd.o(.constdata)
    asc2_3216                                0x08006920   Data        6080  lcd.o(.constdata)
    armBitRevTable                           0x08008180   Data        2048  commontables.o(.constdata)
    twiddleCoef_4096                         0x08008980   Data       32768  commontables.o(.constdata)
    Region$$Table$$Base                      0x08010980   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080109a0   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    g_back_color                             0x20000010   Data           4  lcd.o(.data)
    task_num                                 0x20000014   Data           1  scheduler.o(.data)
    uart_flag                                0x20000024   Data           1  uart_app.o(.data)
    uart_rx_index                            0x20000026   Data           2  uart_app.o(.data)
    uart_rx_ticks                            0x20000028   Data           4  uart_app.o(.data)
    AdcConvEnd                               0x2000002c   Data           1  adc_app.o(.data)
    hadc1                                    0x20000034   Data          72  adc.o(.bss)
    hdma_adc1                                0x2000007c   Data          96  adc.o(.bss)
    htim3                                    0x200000dc   Data          72  tim.o(.bss)
    huart1                                   0x20000124   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x2000016c   Data          96  usart.o(.bss)
    g_sram_handle                            0x200001cc   Data          80  lcd.o(.bss)
    lcddev                                   0x2000021c   Data          14  lcd.o(.bss)
    uart_rx_buffer                           0x2000022a   Data         128  uart_app.o(.bss)
    uart_rx_dma_buffer                       0x200002aa   Data         128  uart_app.o(.bss)
    uart_dma_buffer                          0x2000032a   Data         128  uart_app.o(.bss)
    dac_val_buffer                           0x200003ac   Data        4096  adc_app.o(.bss)
    adc_val_buffer                           0x200013ac   Data        8192  adc_app.o(.bss)
    scfft                                    0x200033ac   Data          20  waveform_analyzer_app.o(.bss)
    FFT_InputBuf                             0x200033c0   Data        8192  waveform_analyzer_app.o(.bss)
    FFT_OutputBuf                            0x200053c0   Data        4096  waveform_analyzer_app.o(.bss)
    __initial_sp                             0x200067c0   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000109d4, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000109a0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO        11122  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO        11247    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO        11250    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO        11252    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO        11254    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO        11255    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO        11262    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO        11257    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO        11259    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO        11248    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c4   0x080001c4   0x00000062   Code   RO        11125    .text               mc_w.l(uldiv.o)
    0x08000226   0x08000226   0x0000001e   Code   RO        11129    .text               mc_w.l(llshl.o)
    0x08000244   0x08000244   0x00000024   Code   RO        11131    .text               mc_w.l(llsshr.o)
    0x08000268   0x08000268   0x00000024   Code   RO        11133    .text               mc_w.l(memcpya.o)
    0x0800028c   0x0800028c   0x00000024   Code   RO        11135    .text               mc_w.l(memseta.o)
    0x080002b0   0x080002b0   0x0000014e   Code   RO        11167    .text               mf_w.l(dadd.o)
    0x080003fe   0x080003fe   0x000000e4   Code   RO        11169    .text               mf_w.l(dmul.o)
    0x080004e2   0x080004e2   0x000000de   Code   RO        11171    .text               mf_w.l(ddiv.o)
    0x080005c0   0x080005c0   0x00000026   Code   RO        11187    .text               mf_w.l(f2d.o)
    0x080005e6   0x080005e6   0x00000002   PAD
    0x080005e8   0x080005e8   0x00000030   Code   RO        11191    .text               mf_w.l(cdrcmple.o)
    0x08000618   0x08000618   0x0000002c   Code   RO        11266    .text               mc_w.l(uidiv.o)
    0x08000644   0x08000644   0x00000020   Code   RO        11268    .text               mc_w.l(llushr.o)
    0x08000664   0x08000664   0x00000000   Code   RO        11277    .text               mc_w.l(iusefp.o)
    0x08000664   0x08000664   0x000000ba   Code   RO        11280    .text               mf_w.l(depilogue.o)
    0x0800071e   0x0800071e   0x00000030   Code   RO        11284    .text               mf_w.l(dfixul.o)
    0x0800074e   0x0800074e   0x00000002   PAD
    0x08000750   0x08000750   0x00000024   Code   RO        11288    .text               mc_w.l(init.o)
    0x08000774   0x08000774   0x0000006e   Code   RO          972    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x080007e2   0x080007e2   0x00000016   Code   RO          973    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x080007f8   0x080007f8   0x0000000a   Code   RO          974    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x08000802   0x08000802   0x00000002   PAD
    0x08000804   0x08000804   0x0000000c   Code   RO          587    i.ADC_IRQHandler    stm32f4xx_it.o
    0x08000810   0x08000810   0x00000128   Code   RO          975    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08000938   0x08000938   0x000001f4   Code   RO         4283    i.Analyze_Frequency_And_Type  waveform_analyzer_app.o
    0x08000b2c   0x08000b2c   0x00000314   Code   RO         4284    i.Analyze_Harmonics  waveform_analyzer_app.o
    0x08000e40   0x08000e40   0x00000002   Code   RO          588    i.BusFault_Handler  stm32f4xx_it.o
    0x08000e42   0x08000e42   0x00000002   PAD
    0x08000e44   0x08000e44   0x00000038   Code   RO         4285    i.Calculate_Phase_Difference  waveform_analyzer_app.o
    0x08000e7c   0x08000e7c   0x0000000c   Code   RO          589    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x08000e88   0x08000e88   0x0000000c   Code   RO          590    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000e94   0x08000e94   0x00000028   Code   RO         1709    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08000ebc   0x08000ebc   0x00000054   Code   RO         1710    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08000f10   0x08000f10   0x00000028   Code   RO         1711    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08000f38   0x08000f38   0x00000002   Code   RO          591    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000f3a   0x08000f3a   0x00000002   PAD
    0x08000f3c   0x08000f3c   0x00000030   Code   RO           13    i.Delay_us          main.o
    0x08000f6c   0x08000f6c   0x00000004   Code   RO           14    i.Error_Handler     main.o
    0x08000f70   0x08000f70   0x00000010   Code   RO         4287    i.Get_Component_Phase  waveform_analyzer_app.o
    0x08000f80   0x08000f80   0x00000060   Code   RO         4290    i.Get_Waveform_Info  waveform_analyzer_app.o
    0x08000fe0   0x08000fe0   0x000000c0   Code   RO         4291    i.Get_Waveform_Phase  waveform_analyzer_app.o
    0x080010a0   0x080010a0   0x00000098   Code   RO         4294    i.Get_Waveform_Vpp  waveform_analyzer_app.o
    0x08001138   0x08001138   0x00000002   Code   RO         1146    i.HAL_ADCEx_InjectedConvCpltCallback  stm32f4xx_hal_adc_ex.o
    0x0800113a   0x0800113a   0x00000002   PAD
    0x0800113c   0x0800113c   0x0000014c   Code   RO          977    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08001288   0x08001288   0x0000001c   Code   RO         4224    i.HAL_ADC_ConvCpltCallback  adc_app.o
    0x080012a4   0x080012a4   0x00000002   Code   RO          979    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x080012a6   0x080012a6   0x00000002   Code   RO          981    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x080012a8   0x080012a8   0x0000012e   Code   RO          985    i.HAL_ADC_IRQHandler  stm32f4xx_hal_adc.o
    0x080013d6   0x080013d6   0x00000054   Code   RO          986    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x0800142a   0x0800142a   0x00000002   Code   RO          987    i.HAL_ADC_LevelOutOfWindowCallback  stm32f4xx_hal_adc.o
    0x0800142c   0x0800142c   0x000000a4   Code   RO          434    i.HAL_ADC_MspInit   adc.o
    0x080014d0   0x080014d0   0x00000158   Code   RO          993    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x08001628   0x08001628   0x0000006c   Code   RO          996    i.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x08001694   0x08001694   0x00000092   Code   RO         1712    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08001726   0x08001726   0x00000024   Code   RO         1713    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x0800174a   0x0800174a   0x00000002   PAD
    0x0800174c   0x0800174c   0x000001a0   Code   RO         1717    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x080018ec   0x080018ec   0x000000d4   Code   RO         1718    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080019c0   0x080019c0   0x0000006e   Code   RO         1722    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08001a2e   0x08001a2e   0x00000002   PAD
    0x08001a30   0x08001a30   0x000001f0   Code   RO         1605    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08001c20   0x08001c20   0x0000000a   Code   RO         1609    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001c2a   0x08001c2a   0x00000002   PAD
    0x08001c2c   0x08001c2c   0x0000000c   Code   RO         2155    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001c38   0x08001c38   0x00000010   Code   RO         2161    i.HAL_IncTick       stm32f4xx_hal.o
    0x08001c48   0x08001c48   0x00000034   Code   RO         2162    i.HAL_Init          stm32f4xx_hal.o
    0x08001c7c   0x08001c7c   0x00000040   Code   RO         2163    i.HAL_InitTick      stm32f4xx_hal.o
    0x08001cbc   0x08001cbc   0x00000034   Code   RO          687    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08001cf0   0x08001cf0   0x0000001a   Code   RO         1997    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001d0a   0x08001d0a   0x00000002   PAD
    0x08001d0c   0x08001d0c   0x00000040   Code   RO         2003    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001d4c   0x08001d4c   0x00000024   Code   RO         2004    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001d70   0x08001d70   0x00000134   Code   RO         1251    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08001ea4   0x08001ea4   0x0000000c   Code   RO         1256    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08001eb0   0x08001eb0   0x00000020   Code   RO         1258    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08001ed0   0x08001ed0   0x00000020   Code   RO         1259    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001ef0   0x08001ef0   0x00000060   Code   RO         1260    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001f50   0x08001f50   0x0000036c   Code   RO         1263    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080022bc   0x080022bc   0x00000028   Code   RO         2008    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080022e4   0x080022e4   0x00000090   Code   RO         3125    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08002374   0x08002374   0x0000005a   Code   RO         2402    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x080023ce   0x080023ce   0x00000002   PAD
    0x080023d0   0x080023d0   0x00000028   Code   RO          503    i.HAL_TIM_Base_MspInit  tim.o
    0x080023f8   0x080023f8   0x00000078   Code   RO         2405    i.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x08002470   0x08002470   0x000000dc   Code   RO         2411    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x0800254c   0x0800254c   0x0000004a   Code   RO         3383    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08002596   0x08002596   0x00000002   PAD
    0x08002598   0x08002598   0x00000058   Code   RO         4164    i.HAL_UARTEx_RxEventCallback  uart_app.o
    0x080025f0   0x080025f0   0x00000070   Code   RO         3397    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08002660   0x08002660   0x00000002   Code   RO         3399    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08002662   0x08002662   0x00000002   PAD
    0x08002664   0x08002664   0x00000280   Code   RO         3402    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080028e4   0x080028e4   0x00000064   Code   RO         3403    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08002948   0x08002948   0x000000b0   Code   RO          545    i.HAL_UART_MspInit  usart.o
    0x080029f8   0x080029f8   0x0000001c   Code   RO         3408    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08002a14   0x08002a14   0x0000003c   Code   RO         4165    i.HAL_UART_RxCpltCallback  uart_app.o
    0x08002a50   0x08002a50   0x00000002   Code   RO         3410    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08002a52   0x08002a52   0x000000a0   Code   RO         3411    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08002af2   0x08002af2   0x00000002   Code   RO         3414    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08002af4   0x08002af4   0x00000002   Code   RO          592    i.HardFault_Handler  stm32f4xx_it.o
    0x08002af6   0x08002af6   0x00000002   PAD
    0x08002af8   0x08002af8   0x00000074   Code   RO          435    i.MX_ADC1_Init      adc.o
    0x08002b6c   0x08002b6c   0x0000003c   Code   RO          475    i.MX_DMA_Init       dma.o
    0x08002ba8   0x08002ba8   0x000000c4   Code   RO          301    i.MX_GPIO_Init      gpio.o
    0x08002c6c   0x08002c6c   0x00000068   Code   RO          504    i.MX_TIM3_Init      tim.o
    0x08002cd4   0x08002cd4   0x00000050   Code   RO          546    i.MX_USART1_UART_Init  usart.o
    0x08002d24   0x08002d24   0x000000f4   Code   RO         4296    i.Map_Input_To_FFT_Frequency  waveform_analyzer_app.o
    0x08002e18   0x08002e18   0x00000002   Code   RO          593    i.MemManage_Handler  stm32f4xx_it.o
    0x08002e1a   0x08002e1a   0x00000002   PAD
    0x08002e1c   0x08002e1c   0x00000010   Code   RO         4297    i.My_FFT_Init       waveform_analyzer_app.o
    0x08002e2c   0x08002e2c   0x00000002   Code   RO          594    i.NMI_Handler       stm32f4xx_it.o
    0x08002e2e   0x08002e2e   0x00000002   Code   RO          595    i.PendSV_Handler    stm32f4xx_it.o
    0x08002e30   0x08002e30   0x00000002   Code   RO          596    i.SVC_Handler       stm32f4xx_it.o
    0x08002e32   0x08002e32   0x00000004   Code   RO          597    i.SysTick_Handler   stm32f4xx_it.o
    0x08002e36   0x08002e36   0x00000002   PAD
    0x08002e38   0x08002e38   0x00000094   Code   RO           15    i.SystemClock_Config  main.o
    0x08002ecc   0x08002ecc   0x00000010   Code   RO         3737    i.SystemInit        system_stm32f4xx.o
    0x08002edc   0x08002edc   0x000000d0   Code   RO         2495    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08002fac   0x08002fac   0x00000014   Code   RO         2506    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08002fc0   0x08002fc0   0x00000010   Code   RO         2507    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08002fd0   0x08002fd0   0x00000022   Code   RO         2513    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08002ff2   0x08002ff2   0x00000024   Code   RO         2515    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08003016   0x08003016   0x0000000e   Code   RO         3416    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08003024   0x08003024   0x0000004a   Code   RO         3417    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x0800306e   0x0800306e   0x00000086   Code   RO         3418    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x080030f4   0x080030f4   0x0000001e   Code   RO         3420    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08003112   0x08003112   0x0000004e   Code   RO         3426    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08003160   0x08003160   0x0000001c   Code   RO         3427    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x0800317c   0x0800317c   0x000000c2   Code   RO         3428    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x0800323e   0x0800323e   0x00000002   PAD
    0x08003240   0x08003240   0x0000010c   Code   RO         3429    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x0800334c   0x0800334c   0x000000a0   Code   RO         3430    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080033ec   0x080033ec   0x00000036   Code   RO         3431    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x08003422   0x08003422   0x00000072   Code   RO         3432    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08003494   0x08003494   0x0000000c   Code   RO          598    i.USART1_IRQHandler  stm32f4xx_it.o
    0x080034a0   0x080034a0   0x00000002   Code   RO          599    i.UsageFault_Handler  stm32f4xx_it.o
    0x080034a2   0x080034a2   0x00000002   PAD
    0x080034a4   0x080034a4   0x00000034   Code   RO        11145    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080034d8   0x080034d8   0x00000026   Code   RO        11214    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x080034fe   0x080034fe   0x00000020   Code   RO         2010    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x0800351e   0x0800351e   0x00000002   PAD
    0x08003520   0x08003520   0x000002ac   Code   RO        10974    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x080037cc   0x080037cc   0x0000003a   Code   RO        11098    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08003806   0x08003806   0x00000006   Code   RO        11218    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x0800380c   0x0800380c   0x00000010   Code   RO        11222    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x0800381c   0x0800381c   0x0000000e   Code   RO        11294    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800382a   0x0800382a   0x00000002   Code   RO        11295    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800382c   0x0800382c   0x0000000e   Code   RO        11296    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800383a   0x0800383a   0x00000002   PAD
    0x0800383c   0x0800383c   0x0000000c   Code   RO        11272    i.__set_errno       mc_w.l(errno.o)
    0x08003848   0x08003848   0x00000184   Code   RO        11147    i._fp_digits        mc_w.l(printfa.o)
    0x080039cc   0x080039cc   0x000006b4   Code   RO        11148    i._printf_core      mc_w.l(printfa.o)
    0x08004080   0x08004080   0x00000024   Code   RO        11149    i._printf_post_padding  mc_w.l(printfa.o)
    0x080040a4   0x080040a4   0x0000002e   Code   RO        11150    i._printf_pre_padding  mc_w.l(printfa.o)
    0x080040d2   0x080040d2   0x00000016   Code   RO        11151    i._snputc           mc_w.l(printfa.o)
    0x080040e8   0x080040e8   0x0000007c   Code   RO         4225    i.adc_task          adc_app.o
    0x08004164   0x08004164   0x00000034   Code   RO         4226    i.adc_tim_dma_init  adc_app.o
    0x08004198   0x08004198   0x000000a8   Code   RO         9410    i.arm_bitreversal_f32  transformfunctions.o
    0x08004240   0x08004240   0x0000003c   Code   RO         9463    i.arm_cfft_radix4_f32  transformfunctions.o
    0x0800427c   0x0800427c   0x000000b0   Code   RO         9464    i.arm_cfft_radix4_init_f32  transformfunctions.o
    0x0800432c   0x0800432c   0x00000044   Code   RO         5332    i.arm_cmplx_mag_f32  complexmathfunctions.o
    0x08004370   0x08004370   0x00000144   Code   RO         9518    i.arm_radix4_butterfly_f32  transformfunctions.o
    0x080044b4   0x080044b4   0x00000216   Code   RO         9520    i.arm_radix4_butterfly_inverse_f32  transformfunctions.o
    0x080046ca   0x080046ca   0x00000002   PAD
    0x080046cc   0x080046cc   0x00000014   Code   RO           16    i.delay_ms          main.o
    0x080046e0   0x080046e0   0x000002d8   Code   RO         4228    i.display_harmonic_results  adc_app.o
    0x080049b8   0x080049b8   0x00000016   Code   RO         3783    i.lcd_draw_point    lcd.o
    0x080049ce   0x080049ce   0x00000002   PAD
    0x080049d0   0x080049d0   0x00000048   Code   RO         3792    i.lcd_fill          lcd.o
    0x08004a18   0x08004a18   0x00000010   Code   RO         3795    i.lcd_pow           lcd.o
    0x08004a28   0x08004a28   0x000000f4   Code   RO         3799    i.lcd_set_cursor    lcd.o
    0x08004b1c   0x08004b1c   0x000000dc   Code   RO         3801    i.lcd_show_char     lcd.o
    0x08004bf8   0x08004bf8   0x00000094   Code   RO         3802    i.lcd_show_float    lcd.o
    0x08004c8c   0x08004c8c   0x00000072   Code   RO         3803    i.lcd_show_num      lcd.o
    0x08004cfe   0x08004cfe   0x00000056   Code   RO         3804    i.lcd_show_string   lcd.o
    0x08004d54   0x08004d54   0x0000008a   Code   RO         3805    i.lcd_show_xnum     lcd.o
    0x08004dde   0x08004dde   0x00000018   Code   RO         3807    i.lcd_wr_data       lcd.o
    0x08004df6   0x08004df6   0x00000018   Code   RO         3808    i.lcd_wr_regno      lcd.o
    0x08004e0e   0x08004e0e   0x00000002   PAD
    0x08004e10   0x08004e10   0x00000014   Code   RO         3809    i.lcd_write_ram_prepare  lcd.o
    0x08004e24   0x08004e24   0x00000034   Code   RO           17    i.main              main.o
    0x08004e58   0x08004e58   0x0000003c   Code   RO         4166    i.my_printf         uart_app.o
    0x08004e94   0x08004e94   0x000001bc   Code   RO         4229    i.print_harmonic_data  adc_app.o
    0x08005050   0x08005050   0x0000000c   Code   RO         4076    i.scheduler_init    scheduler.o
    0x0800505c   0x0800505c   0x0000005c   Code   RO         4039    i.test_tlc5615_voltage  tlc5615.o
    0x080050b8   0x080050b8   0x00000088   Code   RO         4040    i.write_5615        tlc5615.o
    0x08005140   0x08005140   0x00000008   Data   RO         1724    .constdata          stm32f4xx_hal_dma.o
    0x08005148   0x08005148   0x00000010   Data   RO         3738    .constdata          system_stm32f4xx.o
    0x08005158   0x08005158   0x00000008   Data   RO         3739    .constdata          system_stm32f4xx.o
    0x08005160   0x08005160   0x00002f80   Data   RO         3812    .constdata          lcd.o
    0x080080e0   0x080080e0   0x000000a0   Data   RO         4300    .constdata          waveform_analyzer_app.o
    0x08008180   0x08008180   0x00000800   Data   RO         5077    .constdata          commontables.o
    0x08008980   0x08008980   0x00008000   Data   RO         5095    .constdata          commontables.o
    0x08010980   0x08010980   0x00000020   Data   RO        11292    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080109a0, Size: 0x000067c0, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080109a0   0x0000000c   Data   RW         2169    .data               stm32f4xx_hal.o
    0x2000000c   0x080109ac   0x00000004   Data   RW         3740    .data               system_stm32f4xx.o
    0x20000010   0x080109b0   0x00000004   Data   RW         3814    .data               lcd.o
    0x20000014   0x080109b4   0x00000010   Data   RW         4078    .data               scheduler.o
    0x20000024   0x080109c4   0x00000008   Data   RW         4169    .data               uart_app.o
    0x2000002c   0x080109cc   0x00000001   Data   RW         4231    .data               adc_app.o
    0x2000002d   0x080109cd   0x00000003   PAD
    0x20000030   0x080109d0   0x00000004   Data   RW        11273    .data               mc_w.l(errno.o)
    0x20000034        -       0x000000a8   Zero   RW          436    .bss                adc.o
    0x200000dc        -       0x00000048   Zero   RW          505    .bss                tim.o
    0x20000124        -       0x000000a8   Zero   RW          547    .bss                usart.o
    0x200001cc        -       0x0000005e   Zero   RW         3811    .bss                lcd.o
    0x2000022a        -       0x00000180   Zero   RW         4168    .bss                uart_app.o
    0x200003aa   0x080109d4   0x00000002   PAD
    0x200003ac        -       0x00003000   Zero   RW         4230    .bss                adc_app.o
    0x200033ac        -       0x00003014   Zero   RW         4299    .bss                waveform_analyzer_app.o
    0x200063c0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x080109d4, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       280         28          0          0        168       1783   adc.o
      1376        418          0          1      12288       3957   adc_app.o
         0          0      34816          0          0      13473   commontables.o
        68          6          0          0          0       2052   complexmathfunctions.o
        60          4          0          0          0        778   dma.o
       196         14          0          0          0     707839   gpio.o
      1128         36      12160          4         94      13493   lcd.o
       272         22          0          0          0     696116   main.o
        12          4          0         16          0       1080   scheduler.o
        36          8        392          0       1024        832   startup_stm32f407xx.o
       144         24          0         12          0       8753   stm32f4xx_hal.o
      1614         66          0          0          0       9245   stm32f4xx_hal_adc.o
         2          0          0          0          0        977   stm32f4xx_hal_adc_ex.o
       198         14          0          0          0      33799   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7322   stm32f4xx_hal_dma.o
       506         46          0          0          0       2180   stm32f4xx_hal_gpio.o
        52          4          0          0          0        862   stm32f4xx_hal_msp.o
      1356         78          0          0          0       5716   stm32f4xx_hal_rcc.o
       744         74          0          0          0       6996   stm32f4xx_hal_tim.o
       144         28          0          0          0       1368   stm32f4xx_hal_tim_ex.o
      2268         28          0          0          0      16556   stm32f4xx_hal_uart.o
        68         24          0          0          0       6019   stm32f4xx_it.o
        16          4         24          4          0       1115   system_stm32f4xx.o
       144         18          0          0         72       1650   tim.o
       228          4          0          0          0       1184   tlc5615.o
      1262         32          0          0          0       7121   transformfunctions.o
       208         44          0          8        384       3245   uart_app.o
       256         34          0          0        168       1769   usart.o
      2060        196        160          0      12308      11990   waveform_analyzer_app.o

    ----------------------------------------------------------------------
     15822       <USER>      <GROUP>         48      26508    1569270   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          0          0          3          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       684         90          0          0          0        208   atan2f.o
        38          0          0          0          0        116   fpclassifyf.o
        22          6          0          0          0        232   funder.o
        58          0          0          0          0        136   sqrtf.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2260         86          0          0          0        528   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      4586        <USER>          <GROUP>          4          0       2632   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       802         96          0          0          0        692   m_wm.l
      2674        108          0          4          0       1216   mc_w.l
      1104          0          0          0          0        724   mf_w.l

    ----------------------------------------------------------------------
      4586        <USER>          <GROUP>          4          0       2632   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     20408       1478      47592         52      26508    1558670   Grand Totals
     20408       1478      47592         52      26508    1558670   ELF Image Totals
     20408       1478      47592         52          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                68000 (  66.41kB)
    Total RW  Size (RW Data + ZI Data)             26560 (  25.94kB)
    Total ROM Size (Code + RO Data + RW Data)      68052 (  66.46kB)

==============================================================================

